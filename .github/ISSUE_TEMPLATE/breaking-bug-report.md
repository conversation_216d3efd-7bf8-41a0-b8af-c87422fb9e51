---
name: Breaking bug report
about: Create a report about a breaking bug
title: "[BUG: Breaking]"
labels: 'bug: breaking'
assignees: ''

---

## 🧨 Describe the Bug

A clear and concise description of the breaking issue (e.g., crash, OOM, exception, etc).

## 📄 Input Document

Attach the PDF or input file that triggered the error.

## 📤 Output Trace / Stack Trace

Paste the **complete** stack trace or error output, if available.

<details>
<summary>Click to expand</summary>

```
Paste stack trace here
```

</details>

## ⚙️ Environment

Please fill in all relevant details:

- **Marker version**: 
- **Surya version**: 
- **Python version**: 
- **PyTorch version**: 
- **Transformers version**: 
- **Operating System** (incl. container info if relevant): 

## ✅ Expected Behavior

What did you expect <PERSON><PERSON> to do?

## 📟 Command or Code Used

Paste the **exact bash command** or **Python code** you used to run Marker:

<details>
<summary>Click to expand</summary>

```bash
# or Python code block
your_command_here --with-flags
```

</details>

## 📎 Additional Context

Any other context that might help us debug this (e.g., CLI options, working directory, runtime settings).
