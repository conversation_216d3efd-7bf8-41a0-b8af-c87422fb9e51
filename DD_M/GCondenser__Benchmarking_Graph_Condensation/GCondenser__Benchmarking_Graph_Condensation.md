# GCondenser: Benchmarking Graph Condensation

<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>

{yilun.liu, r.qiu, helen.huang}@uq.edu.au The University of Queensland

## Abstract

Large-scale graphs are valuable for graph representation learning, yet the abundant data in these graphs hinders the efficiency of the training process. Graph condensation (GC) alleviates this issue by compressing the large graph into a significantly smaller one that still supports effective model training. Although recent research has introduced various approaches to improve the effectiveness of the condensed graph, evaluations in a more comprehensive and practical manner are not sufficiently explored. This paper proposes the first large-scale graph condensation benchmark, GCondenser, to holistically evaluate and compare mainstream GC methods. GCondenser includes a standardised GC paradigm with condensation, validation, and evaluation procedures, as well as straightforward extensions to new GC methods and datasets. Furthermore, a comprehensive study of GC methods is conducted, presenting insights into the different dimensions of condensation effectiveness. GCondenser is open-sourced and available at <https://github.com/superallen13/GCondenser>.

## 1 Introduction

In graph representation learning, while large graphs are ideal for optimising graph neural networks (GNNs), they often result in training inefficiencies. Traditional data-centric solutions typically involve sampling large graphs to create representative subgraphs. Although the size of the sampled subgraph becomes significantly smaller, the quality of learned graph representations based on the sampling cannot be guaranteed. Graph condensation (GC) has recently emerged as a graph reduction technique that generates small graphs with synthetic nodes and edges, enabling efficient and effective training of graph models [\[14,](#page-10-0) [11\]](#page-9-0). This technique enhances the efficiency of various graph learning applications, such as graph continual learning [\[19,](#page-10-1) [20\]](#page-10-2), inference acceleration [\[8\]](#page-9-1), federated graph learning [\[21\]](#page-10-3) and graph neural architecture search [\[4\]](#page-9-2).

Although numerous GC methods have been proposed and have shown promising performance [\[14,](#page-10-0) [13,](#page-10-4) [17,](#page-10-5) [19,](#page-10-1) [20,](#page-10-2) [36\]](#page-11-0), there is a lack of diverse perspectives of evaluations and comprehensive comparisons among these methods. Therefore, this paper proposes the first large-scale graph condensation benchmark, GCondenser, which aims to assess GC methods across multiple dimensions, including model training efficacy, condensation efficiency, cross-architecture transferability, and performance in continual graph learning. The benchmark covers a thorough collection of gradient matching methods [\[14,](#page-10-0) [13\]](#page-10-4), distribution matching methods [\[17,](#page-10-5) [19,](#page-10-1) [20\]](#page-10-2), and trajectory matching methods [\[36\]](#page-11-0). Moreover, GCondenser standardise the graph condensation paradigm, encompassing condensation, validation and evaluation processes to ensure unified comparisons and simplify the extension to new methods and datasets. Extensive experiments are conducted with GCondenser on various methods and datasets. The contributions of GCondenser can be summarised as follows:

• A comprehensive benchmark is developed to evaluate mainstream GC methods from multiple critical perspectives, including model training effectiveness, condensation efficiency, cross-architecture transferability, performance in continual graph learning etc.

- A standardised graph condensation paradigm is proposed, from condensation, and validation, to evaluation processes, simplifying the implementation and comparisons of graph condensation methods on various datasets.
- Extensive experiments are conducted, which consider not only the hyperparameters of GC methods but also integral components, such as initialisation strategies for condensation, choices of backbone models, validation, downstream tasks etc.
- To exploit the potential of existing methods, a thorough tuning of hyperparameters has been explored to improve the quality of condensed graphs generated by existing GC methods.
- Insightful findings are provided. (a) Condensed graphs without edges can achieve state-ofthe-art quality and benefit from an efficient optimisation process. (b) Initialisation strategies for condensed graphs also influence the final condensation quality. (c) The selection of condensation backbones is sensitive to different categories of methods. (d) Reliable and high-quality condensed graphs depend on suitable validators.
- GCondenser is open-sourced to enable a seamless extension of new methods, graph learning applications and datasets.

## 2 Preliminary

For a graph learning problem, a graph is denoted as G = {A, X,Y }, where the adjacency matrix <sup>A</sup> <sup>∈</sup> <sup>R</sup> <sup>n</sup>×<sup>n</sup> denotes the graph structure, and <sup>X</sup> <sup>∈</sup> <sup>R</sup> n×d is the d-dimensional feature matrix for n nodes. <sup>Y</sup> <sup>∈</sup> <sup>R</sup> <sup>n</sup> represents node labels from a class set <sup>C</sup>.

Graph condensation aims to synthesise a smaller graph G ′ <sup>=</sup> {<sup>A</sup> ′ , X′ ,Y ′ } from a larger original graph G = {A, X,Y } while ensuring that the model trained with G ′ performs similarly to the model trained with G. This objective can be described as a bi-level optimisation problem:

$$
\min_{\mathcal{G}'} \mathcal{L}_{\text{task}}(\mathcal{G}; \boldsymbol{\theta}') \quad \text{s.t. } \boldsymbol{\theta}' = \arg\min_{\boldsymbol{\theta}} \mathcal{L}_{\text{task}}(\mathcal{G}'; \boldsymbol{\theta}),
$$
\n(1)

where the lower level optimisation aims to obtain the graph model weight, θ ′ , by minimising the task-related loss, Ltask, on the condensed graph, G ′ . With θ ′ , the ultimate goal is to obtain the optimal G ′ that minimises Ltask(G; θ ′ ) at the upper level. However, a direct tackling of this optimisation problem is nontrivial, which leads to a more convenient and effective matching-based paradigm:

$$
\min_{\mathcal{G}'} \mathcal{L}_{\text{match}}(\mathcal{G}, \mathcal{G}'; \boldsymbol{\theta}),\tag{2}
$$

where Lmatch is the loss function to measure the distance of key statistics between G and G ′ given model parameter θ. Different GC methods can be categorised by the choice of statistics:

Gradient matching [\[13,](#page-10-4) [14,](#page-10-0) [30\]](#page-10-6) methods match model learning gradients between the original graph ∇θLtask(G; θ) and the condensed graph ∇θLtask(G ′ ; θ). The loss Lmatch can be defined as:

$$
\mathcal{L}_{match} = D(\nabla_{\theta} \mathcal{L}_{task}(A, X, Y; \theta), \nabla_{\theta} \mathcal{L}_{task}(A', X', Y'; \theta)),
$$
\n(3)

where <sup>D</sup>(·, ·) is a distance measure of gradients. Generally, the feature matrix <sup>X</sup>′ is optimised directly with Lmatch. To obtain the synthetic adjacency matrix A ′ , GCond [\[14\]](#page-10-0) generates A ′ by mapping X′ with learnable MLPs, A ′ i,j = Sigmoid [MLP(x ′ <sup>i</sup> ⊕ x ′ j ) + MLP(x ′ <sup>j</sup> ⊕ x ′ i )]/2 , where ⊕ represents the concatenate operation. Alternatively, A ′ can also be treated as the identity matrix I in the condensation process. SGDD [\[30\]](#page-10-6) generates A ′ using a learnable generator GEN<sup>ϕ</sup> from random noise Z and optimises this generator by reducing the optimal transport distance [\[5\]](#page-9-3) between the Laplacian Energy Distributions [\[23\]](#page-10-7) of A and A ′ . A more recent work GDEM [\[18\]](#page-10-8) matches the eigenbasis between the condensed graph and the original graph to manage the structure learning.

Distribution matching [\[17,](#page-10-5) [19,](#page-10-1) [20\]](#page-10-2) methods avoid the slow gradient calculation by aligning G and G ′ in the embedding space, corresponding to E for the original graph and E ′ for the condensed graph:

$$
\mathcal{L}_{\text{match}} = D(E, E'), \tag{4}
$$

where D can be implemented by distance measurement of empirical distribution of embeddings, such as Maximum Mean Discrepancy (MMD) in [\[19\]](#page-10-1), P c∈C Mean (Ec) <sup>−</sup> Mean E ′ c 2 , where the subscript c denotes a specific class.

<span id="page-2-0"></span>Image /page/2/Figure/0 description: The image is a diagram illustrating a process. It starts with 'G' and transforms to 'G'0, then to a blue box labeled 'Lmatch(G, G'0; θ0)'. This is followed by 'G'1, then a series of dots, and then another blue box labeled 'Lmatch(G, G'K-1; θK-1)'. Finally, it leads to 'G'K. Each 'G'0, 'G'1, and 'G'K has a 'Validator' box below it. The entire process leads to 'optimal G''. Below the main process, there are several green boxes labeled: 'Training Model Performance', 'Condensation Efficiency', 'Condensed Graph Initialisation', 'Validation Approaches', 'Cross-architecture Transferability', and 'Continual Graph Learning'.

Figure 1: The standardised graph condensation paradigm in GCondenser consists of condensation ,

validation , and evaluation modules. G ′ 0 is first initialised from the original graph G and matched with the original graph across K initialised model parameter spaces (i.e., from θ<sup>0</sup> to θK−1). Each matching block represents either multi-step [\[14\]](#page-10-0) or one-step [\[13\]](#page-10-4) matching.

Trajectory matching [\[36,](#page-11-0) [32\]](#page-10-9) methods aim to align the learning dynamics of models trained with G ′ and those with G. This alignment is achieved by matching the parameters of models trained on G ′ with the optimal parameters of models trained on G. The trajectory matching loss is defined as:

$$
\mathcal{L}_{\text{match}} = \mathcal{D}(\boldsymbol{\theta}_{t+N}^*, \boldsymbol{\theta}_{t+M}'),\tag{5}
$$

where D measures the difference between the model parameter θ ∗ <sup>t</sup>+<sup>N</sup> trained with the original graph G for N steps, and the model parameter θ ′ <sup>t</sup>+<sup>M</sup> trained with the condensed graph G ′ for M steps from the same starting point of model parameter θt. Generally, θ ∗ <sup>t</sup>+<sup>N</sup> and θ<sup>t</sup> can be generated offline.

Eigenbasis matching [\[18\]](#page-10-8) methods align condensed graphs and original graphs in the spectral domain via eigenbasis:

$$
\mathcal{L}_e = \sum_{k=1}^K \left\| \boldsymbol{X}^\top \boldsymbol{u}_k \boldsymbol{u}_k^\top \boldsymbol{X} - \boldsymbol{X}'^\top \boldsymbol{u}_k' \boldsymbol{u}_k'^\top \boldsymbol{X}' \right\|_F^2, \tag{6}
$$

where uku ⊤ k and u ′ ku ′⊤ k are the subspaces induced by the k-th eigenvector in the real and synthetic graphs. A discrimination constraint to preserve the category-level information:

$$
\mathcal{L}_d = \sum_{i=1}^C \left( 1 - \frac{\mathbf{H}_i^{\top} \cdot \mathbf{H}_i'}{\|\mathbf{H}_i\| \|\mathbf{H}_i'\|} \right) + \sum_{\substack{i,j=1 \\ i \neq j}}^C \frac{\mathbf{H}_i^{\top} \cdot \mathbf{H}_j'}{\|\mathbf{H}_i\| \|\mathbf{H}_j'\|},\tag{7}
$$

where H = Y <sup>⊤</sup>AX and H′ = Y ′⊤ P<sup>K</sup> <sup>k</sup>=1(1 − λk)u ′ ku ′⊤ k . An additional regularisation is used to constrain the representation space:

$$
\mathcal{L}_o = \left\| \boldsymbol{U}_K^{\prime \top} \boldsymbol{U}_K^{\prime} - \boldsymbol{I}_K \right\|_F^2.
$$
\n
$$
(8)
$$

The overall matching loss function of GDEM is formulated as the weighted sum of three regularisation terms:

$$
\mathcal{L}_{\text{match}} = \alpha \mathcal{L}_e + \beta \mathcal{L}_d + \gamma \mathcal{L}_o. \tag{9}
$$

## 3 Benchmark Design

This section details the benchmark design concerning graph condensation modules and various evaluation methods, as illustrated in Figure [1.](#page-2-0)

### 3.1 Graph Condensation Modules

Initialisation of condensed graph: Most GC methods start from an initialisation of the condensed graph with a given node budget. Y ′ is typically generated according to a specific label distribution, which could be balanced or proportional to the original graph's label distribution. The initialised node feature X′ can be random noise or sampled from X with the given Y ′ .

Condensed graph learning: During the condensation learning procedure, GC methods rely on a series of randomly initialised graph models to calculate the matching loss. These graph models could be trained using the condensed graph under the multi-step matching strategy.

Validation protocol: The validation of graph condensation methods is critical for evaluation. In most existing methods, the validation process is often neglected given that there is no formal validation process. GCondenser develops various validators, such as GNNs and the graph neural tangent kernel (GNTK) [\[6,](#page-9-4) [36\]](#page-11-0). After updating the condensed graph, a validator is developed using this condensed graph and evaluates the performance of the validation set from the original graph. The condensed graph with the highest validation accuracy is later selected as the optimal condensed graph.

## 3.2 Evaluations

With GCondenser, extensive evaluations are conducted for the following research questions (RQs):

RQ1: How does the performance of models trained on condensed graphs compare to those trained on entire datasets, random graphs, and k-Center graphs across different budget constraints?

Training model performance will be evaluated for existing GC methods. The GNN architecture will not change during condensation, validation, and test phases. In the test phase, a randomly initialised GNN model is trained using the condensed graph and tested on the test set from the original graph. The model's test accuracy reflects the essential effectiveness of the condensed training graph.

RQ2: What is the condensation efficiency of different graph condensation methods?

Condensation efficiency reflects the required effort for obtaining a high-quality condensed graph. To evaluate the efficiency, the model performance will be compared against the condensation time. The storage efficiency will be directly evident by the model performance against the budget ratio.

RQ3: How do condensed graph initialisation schemes affect the quality of the condensed graph?

Different initialisation strategies decide the starting point of the condensed graph optimisation. The experiment will study the impact of different initialisation strategies, including label (balanced or proportional) and feature (random noise, random subgraph, or k-Center graph [\[22\]](#page-10-10)).

RQ4: How do different validation approaches impact the performance of graph condensation?

Validation approaches are essential for the graph condensation process. Validators, such as GNTK [\[6\]](#page-9-4) and GNNs are used to select the optimal condensed graph during training. The selected graphs are then compared to compare the effectiveness of the different validation approaches.

RQ5: What is the effectiveness of the condensed graph for different GNN architectures?

Cross-architecture transferability is a key indicator of the generalisability of the condensed graph. To evaluate the cross-architecture transferability of the condensed graph, various model architectures are trained by the condensed graphs generated by different GC methods.

RQ6: How effective are graph condensation methods when applied to continual graph learning?

Continual graph learning (CGL) is a widely studied downstream task for graph condensation. In CGL, a memory bank typically contains representative subgraphs of historical incoming graphs. However, considering the limitations of storage space and the effectiveness of subgraphs, condensed graphs may be a better choice. With the flexible design of GCondenser, GC methods can be easily applied to the continual graph learning task and used to evaluate performance in CGL.

## 4 Experiments

GCondenser currently supports seven benchmark datasets with three small-scale networks (CiteSeer, Cora, and PubMed) and four larger graphs (ogbn-arxiv, ogbn-products, Flickr, and Reddit). The statistics are shown in Table [1.](#page-3-0) Flickr and Reddit are evaluated in the inductive (Ind.) setting, while the others are evaluated in the transductive (Trans.) setting. The details

|  | Table 1: Dataset statistics |
|--|-----------------------------|
|  |                             |

<span id="page-3-0"></span>

| Setting | Dataset       | #Nodes    | #Edges     | #Features | #Classes |
|---------|---------------|-----------|------------|-----------|----------|
|         | CiteSeer      | 3,327     | 4,732      | 3,703     | 6        |
|         | Cora          | 2,708     | 5,429      | 1,433     | 7        |
| Trans.  | PubMed        | 19,717    | 88,648     | 500       | 3        |
|         | ogbn-arxiv    | 169,343   | 1,166,243  | 128       | 40       |
|         | ogbn-products | 2,449,029 | 61,859,140 | 100       | 47       |
| Ind.    | Flickr        | 89,250    | 899,756    | 500       | 7        |
|         | Reddit        | 232,965   | 57,307,946 | 602       | 41       |

of transductive and inductive settings in graph condensation are shown in Figure [5](#page-13-0) in Appendix [A.1.](#page-13-1)

## 4.1 Baselines

Sampling approaches: Random samples a random subgraph from the original graph dataset. k-Center [\[22\]](#page-10-10) trains a GCN for several epochs to obtain the embeddings of the original graph, then samples the k-nearest nodes and retrieves the subgraph from the original graph.

Gradient matching: GCond [\[14\]](#page-10-0) matches the gradients of training a GNN between the condensed graph and the original graph while training this GNN using the updated condensed graph. GCondX is the edge-free variant of GCond. DosCond [\[13\]](#page-10-4) only matches the gradients of training a GNN between the condensed graph and the original graph on the first step of the GNN training. DosCondX is the edge-free variant of DosCond. SGDD [\[30\]](#page-10-6) replaces the feature-related MLP structure generator [\[14\]](#page-10-0) with Graphon to mitigate the structure distribution shift on the spectral domain.

Distribution matching: GCDM [\[17\]](#page-10-5) uses maximum mean discrepancy (MMD) [\[9\]](#page-9-5) to measure the distances between original and condensed graph encoded by the same graph encoder during the GNN training using the condensed graph. GCDMX is the edge-free variant of GCDM. DM [\[19,](#page-10-1) [20\]](#page-10-2) uses one-step GCDM without learning any structures for efficiency.

Trajectory matching: SFGC [\[36\]](#page-11-0) firstly generates training trajectories of training a GNN backbone using the original graph. For the condensation phase, a GNN with the same architecture is trained with the condensed graph, and the model parameters are matched to the generated model parameters. GEOM [\[32\]](#page-10-9) applies graph curriculum learning in the trajectory generation phase and uses expanding window matching and knowledge distillation to improve the quality of the condensed graph in the condensation phase.

Eigenbasis matching: GDEM [\[18\]](#page-10-8) first performs eigendecomposition on the original dataset, then matches the synthetic bases with the real ones to reflect the structure and feature relations.

## 4.2 Implementations

Dataset preprocessing: Row feature normalisation is applied to the CiteSeer, Cora, and PubMed datasets, whilst standardisation is utilised on the Arxiv, Flickr, and Reddit datasets. The Products dataset continues to use the features processed by OGB [\[12\]](#page-9-6).

Initialisation: The original label distribution and k-Center graph are utilised for initialisation by default. Different strategies for initialising the condensed graph are discussed in Section [4.5.](#page-6-0)

GNN usages: SGC and GCN are applied as backbone models. The effectiveness of cross-architecture ability for validation and testing is discussed in Section [4.6](#page-6-1) and [4.7.](#page-7-0)

Hardware: One NVIDIA L40 (42GB) or one NVIDIA V100 (32GB) GPU are used for experiment.

Graph learning packages: GCondenser refines baseline methods to support both popular graph learning packages, DGL and PyG, for convenience.

<span id="page-4-0"></span>

## 4.3 Overall Results (RQ1)

Overall experiments are conducted with both SGC and GCN backbones for all datasets and baselines. For each dataset, three budgets are chosen as per [\[14\]](#page-10-0). The training, validation, and test GNN models are kept consistent for each experiment (e.g., SGC and GCN). Table [2](#page-5-0) shows the node classification accuracy of all baseline methods on seven datasets with three different budgets and two backbone models. For each condensed graph, different GC methods first define the hyperparameter candidates. A Bayesian hyperparameter sampler is then applied for faster hyperparameter tuning by monitoring the validation performance. The top three condensed graphs, based on validation performance for each target graph, are selected to report the average accuracy and standard deviation. To reproduce the experimental results, the choices of hyperparameters are provided in Appendix [A.3.](#page-13-2)

Experimental results show that all GC methods can achieve the highest or second-highest test accuracy in at least one setting. The trajectory-matching methods, SFGC and GEOM, show better performance on larger datasets with relatively small budgets. Specifically, SFGC has the best performance on the

<span id="page-5-0"></span>Table 2: Overall node classification performance. For each dataset with different condensed graph sizes and backbone GNN models, the red and bold results indicate the best performance, while the blue and italic results indicate the runner-up. OOT stands for 'Out of Time' for 48 hours (e.g., GDEM runs out of time during the eigendecomposition phase).

|              |          |             |              |       |                                   | Traditional           |                                                                            | Gradient |                         |           | Distribution                                                                                                   |    |      | Trajectory | Eigenbasis Whole     |          |
|--------------|----------|-------------|--------------|-------|-----------------------------------|-----------------------|----------------------------------------------------------------------------|----------|-------------------------|-----------|----------------------------------------------------------------------------------------------------------------|----|------|------------|----------------------|----------|
|              |          | Dataset GNN | Size         | Ratio |                                   | Random k-Center GCond |                                                                            |          | GCondX DosCond DosCondX | SGDD GCDM | GCDMX                                                                                                          | DM | SFGC |            | GEOM GDEM            |          |
|              |          |             | 30           |       | 0.9% 53.8±0.1                     |                       | 52.7±0.0 71.9±0.6 65.3±0.1 71.1±0.9                                        |          | 64.3±0.4                |           | 71.1±0.1 66.0±2.2 70.1±0.8 64.2±8.5 65.2±0.3 60.1±0.2                                                          |    |      |            | 70.8±0.5             |          |
|              |          | SGC         | 60           |       | 1.8% 61.9±0.0<br>3.6% 68.1±0.0    |                       | 66.8±0.0 71.0±0.6 70.9±0.3 72.0±1.1                                        |          | 70.6±0.1                |           | 69.9±0.1 66.7±0.0 65.8±1.3 65.7±2.5 67.0±0.8 65.2±0.2                                                          |    |      |            | 72.3±0.6             | 70.3±1.0 |
|              | CiteSeer |             | 120          |       |                                   |                       | 68.1±0.0 72.5±1.2 71.5±0.0 70.6±0.2                                        |          | 71.8±0.3                |           | 70.8±0.8 69.1±1.2 68.1±0.2 69.7±0.1 68.8±0.2 67.7±0.3                                                          |    |      |            | 72.8±0.4             |          |
|              |          |             | 30           |       | 0.9% 58.9±0.0<br>1.8% 62.6±0.0    |                       | 65.0±0.0 46.3±7.0 66.7±4.2 69.2±0.5                                        |          | 67.7±1.2                |           | 70.6±1.5 71.2±0.8 72.8±0.3 72.6±0.6 69.7±0.3 69.6±0.6                                                          |    |      |            | 71.7±0.3             |          |
|              |          | GCN         | 60<br>120    |       | 3.6% 69.6±0.0                     |                       | 67.8±0.0 54.2±3.9 69.6±0.4 70.4±1.7<br>69.4±0.0 70.7±0.7 70.9±0.2 47.3±7.3 |          | 70.1±0.2<br>70.4±0.2    |           | 71.5±0.7 71.9±0.7 71.7±0.2 72.2±0.7 69.4±0.0 67.5±0.9<br>71.0±0.7 72.3±1.3 72.5±0.5 72.4±0.1 69.8±0.5 72.1±1.0 |    |      |            | 72.7±0.6<br>73.4±0.4 | 71.4±0.5 |
|              |          |             | 35           |       | 1.3% 59.3±0.0                     |                       |                                                                            |          |                         |           |                                                                                                                |    |      |            |                      |          |
|              |          | SGC         | 70           |       | 2.6% 70.7±0.0                     |                       | 63.8±0.0 80.6±0.1 80.6±0.2 80.6±0.1<br>70.3±0.0 81.0±0.2 79.0±0.3 80.3±0.5 |          | 80.8±0.1<br>79.8±0.2    |           | 62.4±5.5 77.0±0.4 79.9±0.1 77.0±0.5 73.8±1.5 69.2±1.2<br>80.8±0.4 78.9±1.0 79.7±0.3 78.0±1.6 77.5±0.1 69.6±1.5 |    |      |            | 71.0±0.6<br>75.4±0.5 | 79.2±0.6 |
|              |          |             | 140          |       | 5.2% 77.0±0.0                     |                       | 77.1±0.0 80.9±0.4 81.2±0.3 80.8±0.4                                        |          | 80.8±0.5                |           | 81.4±0.4 77.9±0.7 80.1±0.2 79.6±0.6 79.2±0.1 77.3±0.1                                                          |    |      |            | 79.5±0.8             |          |
|              | Cora     |             | 35           |       | 1.3% 63.9±0.1                     |                       | 66.5±0.0 80.5±0.4 79.8±1.4 72.0±8.7                                        |          | 80.1±0.9                |           | 80.5±0.4 78.9±0.8 79.1±0.9 79.2±0.2 79.6±0.2 80.3±1.1                                                          |    |      |            | 68.0±0.1             |          |
|              |          | GCN         | 70           |       | 2.6% 73.0±0.0                     |                       | 71.6±0.0 78.1±3.6 80.6±0.9 79.6±0.7                                        |          | 81.1±0.3                |           | 81.2±0.6 79.4±0.6 80.5±0.3 79.6±0.3 79.5±0.1 81.5±0.8                                                          |    |      |            | 72.8±0.8             | 81.7±0.9 |
|              |          |             | 140          |       | 5.2% 77.1±0.0                     |                       | 76.6±0.0 80.2±1.7 81.5±0.1 80.5±0.7                                        |          | 80.6±0.2                |           | 79.9±1.6 79.9±0.2 80.2±0.5 79.9±0.3 80.1±0.6 82.2±0.4                                                          |    |      |            | 77.4±0.6             |          |
|              |          |             | 15           |       | 0.08% 67.8±0.2                    |                       | 70.5±0.1 75.9±0.7 77.3±0.2 74.2±1.1                                        |          | 75.7±0.5                |           | 76.4±0.9 73.3±1.2 74.0±0.3 72.1±0.9 73.9±0.5 73.8±0.3                                                          |    |      |            | 73.8±0.6             |          |
|              |          | SGC         | 30           |       | 0.15% 72.5±0.2<br>0.3% 75.6±0.0   |                       | 75.8±0.0 75.2±0.0 77.1±0.2 77.2±0.1                                        |          | 77.0±0.1                |           | 78.0±0.3 74.7±0.6 75.2±0.7 75.1±0.6 75.8±0.2 77.4±0.4                                                          |    |      |            | 78.7±0.4             | 76.9±0.1 |
| Transductive | PubMed   |             | 60           |       |                                   |                       | 75.7±0.0 75.7±0.0 76.8±0.1 75.5±0.2                                        |          | 75.5±0.0                |           | 76.1±0.1 76.5±1.1 76.3±0.2 75.8±0.0 75.8±0.0 75.8±0.4                                                          |    |      |            | 79.1±0.1             |          |
|              |          |             | 15           |       | 0.08% 69.8±0.1<br>0.15% 73.7±0.1  |                       | 72.1±0.1 67.6±10.4 77.8±0.3 75.7±0.7                                       |          | 75.7±0.1                |           | 76.7±1.1 75.9±0.6 77.1±0.3 76.0±0.7 78.4±0.1 80.1±0.3                                                          |    |      |            | 73.3±0.6             |          |
|              |          | GCN         | 30<br>60     |       | 0.3% 78.0±0.0                     |                       | 76.4±0.0 74.6±0.8 78.0±0.5 76.8±0.2<br>78.2±0.0 77.2±0.7 78.0±0.1 77.3±1.2 |          | 78.6±0.2<br>78.0±0.1    |           | 78.5±0.4 77.4±0.4 76.8±0.6 77.5±0.1 78.1±0.4 79.7±0.3<br>78.0±1.1 77.6±0.4 78.1±0.3 78.0±0.2 78.5±0.5 79.5±0.4 |    |      |            | 78.3±0.8<br>78.8±0.3 | 79.3±0.3 |
|              |          |             | 90           |       | 0.05% 45.6±0.2                    |                       |                                                                            |          |                         |           | 64.5±0.9 60.8±0.1 59.8±0.3 61.0±0.2 66.1±0.2 62.0±0.5                                                          |    |      |            | OOT                  |          |
|              |          | SGC         | 454          |       | 0.25% 55.2±0.0                    |                       | 51.8±0.2 65.5±0.0 66.0±0.2 62.7±0.6<br>58.2±0.0 66.5±0.5 66.4±0.1 63.7±0.2 |          | 61.6±0.3<br>63.8±0.1    |           | 66.4±0.3 62.7±0.9 61.8±0.5 62.9±0.2 66.7±0.3 62.8±0.7                                                          |    |      |            | OOT                  | 68.8±0.0 |
|              |          |             | 909          |       | 0.5% 58.3±0.0                     |                       | 60.3±0.0 67.2±0.1 67.4±0.3 63.9±0.1                                        |          | 64.3±0.4                |           | 66.9±0.3 62.4±0.2 62.6±0.2 62.5±0.0 66.4±0.3 63.6±0.3                                                          |    |      |            | OOT                  |          |
|              | Arxiv    |             | 90           |       | 0.05% 47.1±0.0                    |                       | 54.5±0.0 53.7±1.6 62.7±0.4 55.6±0.4                                        |          | 61.0±0.5                |           | 55.9±5.8 63.3±0.3 63.8±0.3 64.4±0.5 66.1±0.4 65.5±1.0                                                          |    |      |            | OOT                  |          |
|              |          | GCN         | 454          |       | 0.25% 56.8±0.0                    |                       | 60.3±0.0 64.2±0.2 65.4±0.4 61.6±0.5                                        |          | 64.7±0.2                |           | 63.2±0.3 66.4±0.1 66.7±0.4 67.5±0.3 67.2±0.4 65.8±0.4                                                          |    |      |            | OOT                  | 71.1±0.0 |
|              |          |             | 909          |       | 0.5% 60.3±0.0                     |                       | 62.1±0.0 65.1±0.4 66.6±0.2 63.4±0.4                                        |          | 65.8±0.1                |           | 66.8±0.3 67.6±0.0 67.6±0.3 68.0±0.3 67.8±0.2 66.2±0.5                                                          |    |      |            | OOT                  |          |
|              |          |             | 612          |       | 0.025% 51.6±1.3                   |                       | 48.6±0.6 64.0±0.2 64.6±0.1 62.1±0.1                                        |          | 63.6±0.3                |           | 64.9±0.1 57.7±0.2 58.9±0.1 58.5±0.6 62.2±0.1 61.1±0.4                                                          |    |      |            | OOT                  |          |
|              |          | SGC         | 1225         |       | 0.05% 55.2±0.8<br>0.1% 58.0±0.8   |                       | 52.2±0.7 64.0±0.1 62.4±0.1 61.0±0.3                                        |          | 62.7±0.4                |           | 62.3±0.2 58.2±0.3 61.0±0.1 60.9±0.1 62.2±0.2 62.4±0.2                                                          |    |      |            | OOT                  | 64.7±0.1 |
|              | Products |             | 2449         |       |                                   |                       | 55.4±0.4 64.4±0.4 62.8±0.1 61.4±0.2                                        |          | 62.3±0.2                |           | 64.3±0.3 60.8±0.2 61.3±0.1 61.3±0.2 61.9±0.2 63.1±0.2                                                          |    |      |            | OOT                  |          |
|              |          |             | 612          |       | 0.025% 57.8±0.5<br>0.05% 61.6±0.5 |                       | 55.4±0.8 63.7±0.3 65.5±0.2 61.2±0.3                                        |          | 62.2±0.6                |           | 64.0±0.4 66.5±0.1 68.0±0.3 66.2±0.1 67.1±0.2 68.5±0.3                                                          |    |      |            | OOT                  |          |
|              |          | GCN         | 1225<br>2449 |       | 0.1% 65.3±0.5                     |                       | 57.6±0.7 67.0±0.2 66.6±0.3 62.6±0.4<br>59.1±0.5 68.0±0.2 68.3±0.2 65.8±0.2 |          | 64.2±0.2<br>66.8±0.1    |           | 65.9±0.2 68.4±0.4 68.8±0.1 68.5±0.2 67.9±0.3 69.8±0.3<br>66.1±0.3 68.4±0.3 70.0±0.1 69.8±0.2 70.1±0.3 71.1±0.3 |    |      |            | OOT<br>OOT           | 73.1±0.1 |
|              |          |             |              |       | 0.1% 27.6±0.1                     |                       |                                                                            |          |                         |           |                                                                                                                |    |      |            |                      |          |
|              |          |             | 44<br>223    |       | 0.5% 33.5±0.0                     |                       | 34.5±0.1 43.7±0.5 43.7±0.2 41.8±0.4                                        |          | 42.2±0.1                |           | 43.6±0.3 40.3±0.0 37.5±0.2 45.3±0.2 45.3±0.7 33.6±0.4                                                          |    |      |            | OOT<br>OOT           |          |
|              |          | SGC         | 446          |       | 1% 34.4±0.0                       |                       | 36.1±0.0 42.2±0.2 43.8±0.6 42.6±0.2<br>36.5±0.0 41.1±0.8 43.7±0.3 42.1±0.4 |          | 43.9±0.1<br>43.3±0.1    |           | 41.6±1.6 40.8±0.1 36.2±0.2 43.6±0.1 45.7±0.4 37.4±0.2<br>43.2±0.4 42.7±0.4 34.3±0.3 42.6±0.1 46.1±0.5 38.1±0.2 |    |      |            | OOT                  | 44.2±0.0 |
|              | Flickr   |             | 44           |       | 0.1% 39.2±0.0                     |                       | 40.7±0.0 43.3±0.3 45.4±0.3 43.3±1.1                                        |          | 44.9±0.9                |           | 42.4±0.2 44.5±0.4 44.8±0.5 45.2±0.3 46.9±0.3 44.6±0.5                                                          |    |      |            | OOT                  |          |
|              |          | GCN         | 223          |       | 0.5% 42.4±0.0                     |                       | 41.4±0.0 44.6±0.4 45.8±0.3 43.5±1.0                                        |          | 45.0±0.2                |           | 44.9±0.3 45.0±0.2 46.4±0.2 45.6±0.7 47.0±0.1 45.2±0.9                                                          |    |      |            | OOT                  | 46.8±0.2 |
|              |          |             | 446          |       | 1% 43.4±0.0                       |                       | 41.4±0.0 44.4±0.1 45.7±0.2 42.8±0.2                                        |          | 44.8±0.4                |           | 45.2±0.2 44.6±0.3 46.7±0.2 44.9±0.1 47.2±0.1 45.5±0.1                                                          |    |      |            | OOT                  |          |
| Inductive    |          |             | 153          |       | 0.05% 55.8±0.2                    |                       | 54.0±0.1 89.7±0.6 91.1±0.1 90.8±0.1                                        |          | 91.0±0.0                |           | 90.5±0.3 90.3±0.8 92.0±0.0 92.1±0.0 90.9±0.2 59.4±1.5                                                          |    |      |            | OOT                  |          |
|              |          | SGC         | 769          |       | 0.1% 74.1±0.1                     |                       | 78.6±0.0 91.8±0.2 90.4±0.1 91.5±0.0                                        |          | 91.8±0.0                |           | 91.9±0.0 88.1±2.8 90.9±0.1 91.0±0.0 92.6±0.2 81.7±0.7                                                          |    |      |            | OOT                  | 93.2±0.0 |
|              | Reddit   |             | 1539         |       | 0.2% 83.3±0.0                     |                       | 83.8±0.0 92.1±0.3 90.4±0.1 92.0±0.2                                        |          | 92.1±0.0                |           | 86.3±5.6 91.7±0.2 90.0±0.0 89.6±0.1 92.6±0.3 86.7±0.1                                                          |    |      |            | OOT                  |          |
|              |          |             | 153          |       | 0.05% 60.4±0.0                    |                       | 58.6±0.1 56.8±2.1 85.8±0.1 75.8±1.9                                        |          | 88.6±0.3                |           | 72.9±4.9 88.9±1.2 91.2±0.1 91.3±0.1 89.2±0.5 90.0±0.5                                                          |    |      |            | OOT                  |          |
|              |          | GCN         | 769          |       | 0.1% 81.7±0.0<br>0.2% 87.1±0.0    |                       | 81.7±0.0 87.4±0.4 90.5±0.2 87.9±1.1                                        |          | 91.2±0.2                |           | 89.6±2.5 91.8±0.3 92.4±0.0 92.6±0.2 90.9±0.3 89.4±0.5                                                          |    |      |            | OOT                  | 94.2±0.0 |
|              |          |             | 1539         |       |                                   |                       | 86.9±0.0 91.4±0.4 89.5±0.3 88.2±2.1                                        |          | 91.9±0.1                |           | 91.2±0.3 92.2±0.1 92.7±0.1 92.5±0.3 92.4±0.1 91.2±0.1                                                          |    |      |            | OOT                  |          |

Arxiv dataset, while GDEM demonstrates superiority on the Products dataset. Surprisingly, even the simplest DM methods can effectively condense the graph. In addition, although GDEM performs well on small datasets, it encounters scalability issues with large graph datasets. Our findings are summarised as follows:

Exploiting the potential of GC methods: With effective validation approaches and comprehensive hyperparameter tuning, most methods achieve enhanced and comparable performance.

Different backbones: For gradient matching methods, using SGC as the backbone can produce more robust synthetic graphs, whereas GCN poses challenges due to its extra activation function, additional layers, and increased number of model parameters. For distribution matching methods, a suitable embedding space is crucial. The GCN backbone can encode nodes into a more expressive higher-dimensional space compared to SGC, which only has a linear fully connected layer. For trajectory matching methods, the more expressive GCN results in better synthetic graphs than SGC.

Structure-free: Generally, structure-free variants (e.g., GCondX, DosCondX and GCDMX) can obtain comparable results to condensed graphs with structure (e.g., GCond, DosCond, and GCDM).

## 4.4 Condensation Efficiency (RQ2)

Efficiency is a crucial aspect of evaluating different GC methods. In Figure [2,](#page-6-2) the condensation time is compared against the model performance under the setting of 90-node for the Arxiv dataset. More results for different datasets are shown in Figure [8](#page-15-0) ∼ [13](#page-18-0) of Appendix [B.1.](#page-15-1)

Overall, methods only considering the feature matrix (e.g., GCondX, DosCondX, DM, SFGC) have a higher efficiency, while the methods considering both the feature matrix and the adjacency matrix (e.g., GCond, DosCond, SGDD, GCDM) need more condensation time. Moreover, gradient matching methods (e.g., GCond, GCondX, DosCond. DosCondX and SGDD) require more condensation time

<span id="page-6-2"></span>Image /page/6/Figure/0 description: The image is a scatter plot showing the relationship between condensation time (in minutes) and test accuracy (in percentage) for different methods. The x-axis represents condensation time, ranging from 0 to 50 minutes, and the y-axis represents test accuracy, ranging from 58% to 66%. Several data points are plotted, each representing a different method, with their positions indicating their condensation time and test accuracy. The methods include SFGC, GCondX, DM, DosCond, GCDMX, DosCondX, GCond, SGDD, and GCDMX. The plot also includes a legend indicating the markers used for SGC (square) and GCN (circle).

Figure 2: Test accuracy against condensation time for different GC methods on a 90-node condensed graph from the Arxiv dataset, with backbone models GCN and SGC. More results in Appendix [B.1.](#page-15-1)

than distribution matching and trajectory matching methods. SGC as the backbone can improve the condensation efficiency but the model performance could drop slightly as in GCDM and DM.

For SFGC, extra storage space and time are needed for trajectory generations, as in Table [3.](#page-6-3) By training 200 offline expert GCNs, the overall condensation cost of SFGC will increase significantly, which would lead to either storage issues or a slow condensation process, which is not applicable for online downstream applications, such as continual graph learning in Section [4.8.](#page-7-1)

<span id="page-6-0"></span>

| 4.5 | Condensed Graph Initialisation (RQ3) |  |
|-----|--------------------------------------|--|

Different initialisation strategies for the condensed graphs also affect the optimisation and output quality of GC methods. Stable optimisation and consistent condensed graph quality across different initialisations are desired for practical applications. Table [4](#page-6-4) presents the performance of three methods (GCond, DosCond, and DM) on the Arxiv dataset for two label distributions (balanced and proportional to original) and three feature initialisation methods (random

<span id="page-6-3"></span>Table 3: Storage space and the training time of offline trajectories in SFGC by training 200 GCNs.

|             | CiteSeer | Cora | PubMed | Arxiv | Products | Flickr | Reddit |
|-------------|----------|------|--------|-------|----------|--------|--------|
| Storage (G) | 72       | 28   | 9.8    | 3.3   | 3.1      | 9.9    | 13.3   |
| Time (min)  | 58       | 33   | 50     | 177   | 2,820    | 184    | 1,961  |

<span id="page-6-4"></span>Table 4: Condensation of Arxiv into 90 nodes with different condensed graph initialisation.

| Label        | Feature         | GCond | DosCond | DM   |
|--------------|-----------------|-------|---------|------|
| Balanced     | Random Noise    | 57.4  | 56.1    | 62.4 |
|              | Random Subgraph | 56.2  | 55.2    | 62.9 |
|              | k-Center        | 57.6  | 57.2    | 62.7 |
| Proportional | Random Noise    | 58.4  | 60.4    | 64.5 |
|              | Random Subgraph | 58.4  | 59.4    | 64.1 |
|              | k-Center        | 57.9  | 59.3    | 63.7 |

noise, random subgraph, and k-Center graph). The results show that maintaining the original label distribution leads to better performance than using a balanced label distribution. This is because, for graph data, there is generally a class imbalance issue with a large number of classes (40 classes for Arxiv). Different label initialisations directly constrain the node budget for each class. For instance, Figure [6](#page-13-3) in Appendix [A.2](#page-13-4) illustrates how class sizes vary in the condensed graph under different label initialisations. Keeping the proportion of labels in the condensed graph can largely maintain the performance of the classes with more nodes. While for the feature initialisation, different strategies achieve comparable results across different GC methods.

<span id="page-6-1"></span>

## 4.6 Impact of Different Validators (RQ4)

The validator selection for the graph condensation process is essential. The validator can select a convincing graph during each condensation process and guide the hyperparameter sampler to quickly and accurately find the better hyperparameter combination. For the reliable quality of the selected

<span id="page-7-2"></span>Table 5: Performance of different validators on Cora, with a budget of 35-node and GCN as the backbone. The test accuracy (%) and the relative condensation time to the GCN validator are shown.

| Validator | GCond       | GCondX      | DosCond     | DosCondX    | SGDD        | GCDM        | GCDMX       | DM          | SFGC        | Avg.        |
|-----------|-------------|-------------|-------------|-------------|-------------|-------------|-------------|-------------|-------------|-------------|
| GCN       | 73.8 (1.0x) | 80.6 (1.0x) | 78.3 (1.0x) | 80.7 (1.0x) | 78.7 (1.0x) | 79.4 (1.0x) | 78.9 (1.0x) | 80.2 (1.0x) | 79.6 (1.0x) | 78.9 (1.0x) |
| SGC       | 67.6 (0.5x) | 80.7 (0.7x) | 78.2 (0.8x) | 81.1 (0.9x) | 64.2 (0.7x) | 80.0 (1.1x) | 78.5 (1.0x) | 80.4 (1.0x) | 79.8 (1.0x) | 76.7 (0.9x) |
| GNTK      | 51.8 (0.2x) | 79.7 (0.2x) | 78.7 (0.8x) | 78.8 (0.6x) | 73.3 (0.6x) | 57.6 (1.1x) | 79.5 (0.6x) | 78.0 (0.2x) | 79.3 (0.9x) | 73.0 (0.6x) |

graph and the effectiveness of hyperparameter search, a performance consistency of the validator on the validation set and test set is expected. GCN, SGC and GNTK as validators can serve the graph condensation process to find the optimal graph that optimises the validator to perform best on the validation set. Table [5](#page-7-2) shows the performance of the GCN trained with different validators and GC methods and the relative convergence time compared with GCN. The results indicate that GNN validators (e.g., GCN and SGC) are effective in assessing the quality of the condensed graph. GCN produces better results but requires more time, while SGC, which caches the aggregated features across the graph, requires less time but sacrifices some performance. GNTK is the most efficient validator, but it is not as reliable compared to GNN validators.

<span id="page-7-0"></span>

### 4.7 Cross-architecture Transferability (RQ5)

To test the cross-architecture transferability of optimal condensed graphs from Section [4.3](#page-4-0) After obtaining the selected optimal condensed graph. GCondenser can use the condensed graph to train different GNNs and evaluate the performance of the GNNs on the test set. The benchmark provides a set of architectures, including MLP, SGC [\[28\]](#page-10-11), GCN [\[15\]](#page-10-12), GAT [\[24\]](#page-10-13), ChebNet [\[3\]](#page-9-7), SAGE [\[10\]](#page-9-8), APPNP [\[16\]](#page-10-14). It is worth noting that for MLP evaluation, graph structure information is avoided in both the training and inference phases. Figure [3](#page-7-3) shows the cross-architecture transferability of the 30 node graph condensed from the CiteSeer dataset by different GC methods. The results show that the DosCondX and GCondX methods with the GCN backbone have better average performance. More cross-architecture evaluations with different datasets are shown in Figure [14](#page-18-1) ∼ [19](#page-19-0) of Appendix [B.2.](#page-16-0)

<span id="page-7-3"></span>Image /page/7/Figure/5 description: The image presents two tables side by side, each displaying performance metrics for different models across various datasets. The left table is labeled "Backbone: SGC," and the right table is labeled "Backbone: GCN." Both tables share the same structure, with rows representing different models (GCond, GCondX, DosCond, DosCondX, SGDD, GCD, GCDMX, DM, SFGC) and columns representing different datasets (MLP, SGC, GCN, GAT, ChebNet, SAGE, APPNP, Avg.). The cells of the tables contain numerical values, likely representing performance scores, with a color gradient indicating the magnitude of the values, ranging from 50 to 80.

Figure 3: Transferability of condensed graphs for Cora with budget 35. More results in Appendix [B.2.](#page-16-0)

<span id="page-7-1"></span>

### 4.8 Continual Graph Learning (RQ6)

To explore the effectiveness of different graph condensation methods in downstream tasks, continual graph learning (CGL) is employed for evaluating the condensed graphs. Three representative methods are selected for comparison: GCond for multi-step matching with structure learning, DosCond for one-step matching with structures learning, and DM for one-step matching without structure learning. These three methods are compared against the random subgraph method, the k-Center graph method, and the whole graph upper-bound. It is worth noting that SFGC, as an offline algorithm, is not suitable to apply in continual graph learning, where online updates are required. The Condense and Train (CaT) framework [\[19\]](#page-10-1) under the class incremental learning (class-IL) setting is applied. CiteSeer, Cora, Arxiv, Products, Flickr, and Reddit datasets are divided into a series of incoming subgraphs, each with two new classes. If a subgraph contains only one class, this subgraph should be removed from the streaming data. PubMed is not evaluated as it only contains three classes. Offline methods, such as SFGC, GDEM, and GEOM, are also not evaluated. Due to the running time of SGDD, a large number of tasks would be time-consuming. Therefore, SGDD is not evaluated in the CGL.

Figure [4](#page-8-0) shows that in smaller datasets like CiteSeer and Cora, distribution-matching methods perform better due to the good adaptability of the condensed graphs to the continually learned model. DosCondX can even match the performance of training on the whole graph in long series tasks. However, most methods failed on the Flickr dataset, indicating that this dataset poses challenges for maintaining historical knowledge on the condensed graphs in the CGL setting.

<span id="page-8-0"></span>Image /page/8/Figure/1 description: The image is a figure with six line plots, each depicting the Average Precision (AP) of different methods across multiple tasks. The x-axis represents the 'Task' number, and the y-axis represents the AP, ranging from 0.2 to 1.0. The plots are titled: 'Citeseer', 'Cora', 'Flickr', 'Products', 'Arxiv', and 'Reddit'. Each plot displays multiple lines, each representing a different method: 'Whole', 'Random', 'k-Center', 'GCond', 'GCondX', 'DosCond', 'DosCondX', 'GCDM', 'GCDMX', and 'DM'. The 'Citeseer', 'Cora', and 'Flickr' plots have 3 tasks, while 'Arxiv' and 'Reddit' have 20 tasks, and 'Products' has 23 tasks.

Figure 4: GC methods on baseline datasets under the continual graph learning setting.

## 5 Related Work

Graph condensation methods can be categorised into the following branches. (1) Gradient matching methods align the gradients of GNN models trained on the original and the synthetic graphs, including GCond [\[14\]](#page-10-0), DosCond [\[13\]](#page-10-4), and SGDD [\[30\]](#page-10-6). (2) Distribution matching methods align the embeddings of the original and the synthetic graphs, such as GCDM [\[17\]](#page-10-5), CaT [\[19\]](#page-10-1), and PUMA [\[20\]](#page-10-2). (3) Trajectory matching methods align the parameters of GNN models trained on the original and the synthetic graphs as in SFGC [\[36\]](#page-11-0) and GEOM [\[32\]](#page-10-9). (4) Kernel matching methods rely on graph neural tangent kernel to align the original and the synthetic graphs, including GC-SNTK [\[26\]](#page-10-15) and KIDD [\[29\]](#page-10-16). (5) Eigen matching methods focus on matching the eigen-decomposition features of the structure of the original and the synthetic graphs as in SGDD [\[30\]](#page-10-6) and GDEM [\[18\]](#page-10-8). Note that there are several computer vision dataset condensation methods mainly focused on image data [\[1,](#page-9-9) [25,](#page-10-17) [27,](#page-10-18) [33,](#page-10-19) [34,](#page-10-20) [35\]](#page-11-1), as well as a corresponding benchmark DC-Bench [\[2\]](#page-9-10). However, a direct extension to graph representation learning is nontrivial since graph data involves not only features but also topological structures with interdependence.

Tasks and applications of graph condensation of existing work mainly focus on node classification. Recently, graph classification [\[29\]](#page-10-16), link prediction [\[30\]](#page-10-6), fairness [\[7\]](#page-9-11), and heterogeneous graphs have gradually attracted attention from the research community. Additionally, graph condensation has

served as a powerful tool for various downstream tasks, including continual graph learning [\[19,](#page-10-1) [20\]](#page-10-2), inductive inference [\[8\]](#page-9-1), federated learning [\[21\]](#page-10-3), neural architecture search [\[4\]](#page-9-2), etc.

<span id="page-9-12"></span>

## 6 Conclusion and Future Work

Conclusion: This paper introduces a large-scale graph condensation benchmark, GCondenser, which establishes a standardised GC paradigm to evaluate and compare mainstream GC methods. This benchmark highlights the differences among various GC components and methods. Moreover, a comprehensive experimental study is conducted, demonstrating that GCondenser improve the condensation quality of existing methods through extensive hyperparameter tuning and appropriate validation approaches.

Societal impact: GCondenser provides a unified graph GC paradigm and various GC methods, making the implementation of graph condensation techniques easier. On the positive side, it enhances the development of GC research. On the slightly negative side, the improper use of GC can impact downstream tasks, such as data sensitivity and the robustness of the training model.

Limitation and future work: The current state of GCondenser reflects the development of graph condensation methods on datasets with a relatively small scale. In the near future, GCondenser will be expanded to support more emerging GC methods and additional large-scale graph datasets, such Papers100M [\[12\]](#page-9-6). Further, GCondenser will include a wider collection of downstream applications of graph condensation, such as graph neural architecture search as in GraphGym [\[31\]](#page-10-21), link prediction and graph classification. The straightforward extension to new methods, tasks and datasets will ensure the GCondenser to be an updated and powerful benchmark for graph condensation.

## References

- <span id="page-9-9"></span>[1] G. Cazenavette, T. Wang, A. Torralba, A. A. Efros, and J.-Y. Zhu. Dataset Distillation by Matching Training Trajectories. In *CVPR*, 2022.
- <span id="page-9-10"></span>[2] J. Cui, R. Wang, S. Si, and C.-J. Hsieh. DC-BENCH: Dataset condensation benchmark. In *NeurIPS*, 2022.
- <span id="page-9-7"></span>[3] M. Defferrard, X. Bresson, and P. Vandergheynst. Convolutional neural networks on graphs with fast localized spectral filtering. In *NeurIPS*, 2016.
- <span id="page-9-2"></span>[4] M. Ding, X. Liu, T. Rabbani, and F. Huang. Faster Hyperparameter Search on Graphs via Calibrated Dataset Condensation. In *NeurIPS 2022 Workshop: New Frontiers in Graph Learning*, 2022.
- <span id="page-9-3"></span>[5] Y. Dong and W. Sawin. COPT: Coordinated optimal transport on graphs. In *NeurIPS*, 2020.
- <span id="page-9-4"></span>[6] S. S. Du, K. Hou, R. Salakhutdinov, B. Póczos, R. Wang, and K. Xu. Graph Neural Tangent Kernel: Fusing Graph Neural Networks with Graph Kernels. In *NeurIPS*, 2019.
- <span id="page-9-11"></span>[7] Q. Feng, Z. S. Jiang, R. Li, Y. Wang, N. Zou, J. Bian, and X. Hu. Fair graph distillation. In *NeurIPS*, 2023.
- <span id="page-9-1"></span>[8] X. Gao, T. Chen, Y. Zang, W. Zhang, Q. V. H. Nguyen, K. Zheng, and H. Yin. Graph Condensation for Inductive Node Representation Learning. *CoRR*, abs/2307.15967, 2023.
- <span id="page-9-5"></span>[9] A. Gretton, K. M. Borgwardt, M. J. Rasch, B. Schölkopf, and A. J. Smola. A kernel two-sample test. *J. Mach. Learn. Res.*, 13, 2012.
- <span id="page-9-8"></span>[10] W. L. Hamilton, Z. Ying, and J. Leskovec. Inductive representation learning on large graphs. In *NeurIPS*, 2017.
- <span id="page-9-0"></span>[11] M. Hashemi, S. Gong, J. Ni, W. Fan, B. A. Prakash, and W. Jin. A Comprehensive Survey on Graph Reduction: Sparsification, Coarsening, and Condensation. *CoRR*, abs/2402.03358, 2024.
- <span id="page-9-6"></span>[12] W. Hu, M. Fey, M. Zitnik, Y. Dong, H. Ren, B. Liu, M. Catasta, and J. Leskovec. Open graph benchmark: Datasets for machine learning on graphs. In *NeurIPS*, 2020.

- <span id="page-10-4"></span>[13] W. Jin, X. Tang, H. Jiang, Z. Li, D. Zhang, J. Tang, and B. Yin. Condensing Graphs via One-Step Gradient Matching. In *SIGKDD*, 2022.
- <span id="page-10-0"></span>[14] W. Jin, L. Zhao, S. Zhang, Y. Liu, J. Tang, and N. Shah. Graph condensation for graph neural networks. In *ICLR*, 2022.
- <span id="page-10-12"></span>[15] T. N. Kipf and M. Welling. Semi-supervised classification with graph convolutional networks. In *ICLR*, 2017.
- <span id="page-10-14"></span>[16] J. Klicpera, A. Bojchevski, and S. Günnemann. Predict then propagate: Graph neural networks meet personalized PageRank. In *ICLR*, 2019.
- <span id="page-10-5"></span>[17] M. Liu, S. Li, X. Chen, and L. Song. Graph Condensation via Receptive Field Distribution Matching. *CoRR*, abs/2206.13697, 2022.
- <span id="page-10-8"></span>[18] Y. Liu, D. Bo, and C. Shi. Graph Condensation via Eigenbasis Matching. *CoRR*, abs/2310.09202, 2023.
- <span id="page-10-1"></span>[19] Y. Liu, R. Qiu, and Z. Huang. CaT: Balanced Continual Graph Learning with Graph Condensation. In *ICDM*, 2023.
- <span id="page-10-2"></span>[20] Y. Liu, R. Qiu, Y. Tang, H. Yin, and Z. Huang. PUMA: Efficient Continual Graph Learning with Graph Condensation. *CoRR*, abs/2312.14439, 2023.
- <span id="page-10-3"></span>[21] Q. Pan, R. Wu, T. Liu, T. Zhang, Y. Zhu, and W. Wang. FedGKD: Unleashing the Power of Collaboration in Federated Graph Neural Networks. *CoRR*, abs/2309.09517, 2023.
- <span id="page-10-10"></span>[22] O. Sener and S. Savarese. Active learning for convolutional neural networks: A core-set approach. In *ICLR*, 2018.
- <span id="page-10-7"></span>[23] J. Tang, J. Li, Z. Gao, and J. Li. Rethinking graph neural networks for anomaly detection. In *ICML*, 2022.
- <span id="page-10-13"></span>[24] P. Velickovic, G. Cucurull, A. Casanova, A. Romero, P. Liò, and Y. Bengio. Graph attention networks. In *ICLR*, 2018.
- <span id="page-10-17"></span>[25] K. Wang, B. Zhao, X. Peng, Z. Zhu, S. Yang, S. Wang, G. Huang, H. Bilen, X. Wang, and Y. You. CAFE: Learning to Condense Dataset by Aligning Features. In *CVPR*, 2022.
- <span id="page-10-15"></span>[26] L. Wang, W. Fan, J. Li, Y. Ma, and Q. Li. Fast graph condensation with structure-based neural tangent kernel. In *WWW*, 2024.
- <span id="page-10-18"></span>[27] T. Wang, J.-Y. Zhu, A. Torralba, and A. A. Efros. Dataset distillation. *CoRR*, abs/1811.10959, 2018.
- <span id="page-10-11"></span>[28] F. Wu, A. H. S. Jr., T. Zhang, C. Fifty, T. Yu, and K. Q. Weinberger. Simplifying graph convolutional networks. In *ICML*, 2019.
- <span id="page-10-16"></span>[29] Z. Xu, Y. Chen, M. Pan, H. Chen, M. Das, H. Yang, and H. Tong. Kernel Ridge Regression-Based Graph Dataset Distillation. In *SIGKDD*, 2023.
- <span id="page-10-6"></span>[30] B. Yang, K. Wang, Q. Sun, C. Ji, X. Fu, H. Tang, Y. You, and J. Li. Does graph distillation see like vision dataset counterpart? In *NeurIPS*, 2023.
- <span id="page-10-21"></span>[31] J. You, Z. Ying, and J. Leskovec. Design space for graph neural networks. In *NeurIPS*, 2020.
- <span id="page-10-9"></span>[32] Y. Zhang, T. Zhang, K. Wang, Z. Guo, Y. Liang, X. Bresson, W. Jin, and Y. You. Navigating complexity: Toward lossless graph condensation via expanding window matching. *CoRR*, abs/2402.05011, 2024.
- <span id="page-10-19"></span>[33] B. Zhao and H. Bilen. Dataset condensation with differentiable siamese augmentation. In *ICML*, 2021.
- <span id="page-10-20"></span>[34] B. Zhao and H. Bilen. Dataset Condensation with Distribution Matching. In *WACV*, 2023.

- <span id="page-11-1"></span>[35] B. Zhao, K. R. Mopuri, and H. Bilen. Dataset condensation with gradient matching. In *ICLR*, 2021.
- <span id="page-11-0"></span>[36] X. Zheng, M. Zhang, C. Chen, Q. V. H. Nguyen, X. Zhu, and S. Pan. Structure-free graph condensation: From large-scale graphs to condensed graph-free data. In *NeurIPS*, 2023.

## Checklist

- 1. For all authors...
  - (a) Do the main claims made in the abstract and introduction accurately reflect the paper's contributions and scope? [Yes]
  - (b) Did you describe the limitations of your work? [Yes] Section [6](#page-9-12)
  - (c) Did you discuss any potential negative societal impacts of your work? [Yes] Section [6](#page-9-12)
  - (d) Have you read the ethics review guidelines and ensured that your paper conforms to them? [Yes]
- 2. If you are including theoretical results...
  - (a) Did you state the full set of assumptions of all theoretical results? [N/A]
  - (b) Did you include complete proofs of all theoretical results? [N/A]
- 3. If you ran experiments (e.g. for benchmarks)...
  - (a) Did you include the code, data, and instructions needed to reproduce the main experimental results (either in the supplemental material or as a URL)? [Yes]
  - (b) Did you specify all the training details (e.g., data splits, hyperparameters, how they were chosen)? [Yes]
  - (c) Did you report error bars (e.g., with respect to the random seed after running experiments multiple times)? [Yes]
  - (d) Did you include the total amount of compute and the type of resources used (e.g., type of GPUs, internal cluster, or cloud provider)? [Yes]
- 4. If you are using existing assets (e.g., code, data, models) or curating/releasing new assets...
  - (a) If your work uses existing assets, did you cite the creators? [Yes]
  - (b) Did you mention the license of the assets? [Yes]
  - (c) Did you include any new assets either in the supplemental material or as a URL? [Yes]
  - (d) Did you discuss whether and how consent was obtained from people whose data you're using/curating? [Yes]
  - (e) Did you discuss whether the data you are using/curating contains personally identifiable information or offensive content? [Yes]
- 5. If you used crowdsourcing or conducted research with human subjects...
  - (a) Did you include the full text of instructions given to participants and screenshots, if applicable? [N/A]
  - (b) Did you describe any potential participant risks, with links to Institutional Review Board (IRB) approvals, if applicable? [N/A]
  - (c) Did you include the estimated hourly wage paid to participants and the total amount spent on participant compensation? [N/A]

# A Implementation Details

<span id="page-13-1"></span>

## A.1 Graph Condensation Settings

Transductive and inductive are two settings for graph dataset condensation, as introduced by [\[14\]](#page-10-0). The differences between these settings are illustrated in Figure [5.](#page-13-0) In the transductive setting, test nodes and their induced edges are available during training, whereas in the inductive setting, these nodes and edges are unavailable. In the testing phase of the inductive setting, the availability of labelled nodes and their induced edges is conditional. This paper assumes that labelled nodes are unavailable during the test phase in the inductive setting.

<span id="page-13-0"></span>Image /page/13/Figure/3 description: The image is a diagram illustrating the concepts of transductive and inductive settings in the context of graph neural networks (GNNs). It depicts two main settings: Transductive and Inductive, each with two GNN models, GNNorig and GNNcond. The diagram shows the flow of data and operations. In the transductive setting, the input is processed by GNNorig, and the output is predicted. The input is also processed by GNNcond, and the output is predicted. The two outputs are similar. The same process is repeated in the inductive setting. The inputs are processed by GNNorig and GNNcond, and the outputs are predicted and similar. The GCondenser is in the middle, taking inputs and passing them to the two settings.

Figure 5: GCondenser for graph condensation with transductive (left) and inductive (right) settings.

<span id="page-13-4"></span>

## A.2 Label Distribution

GCondenser provides two label distribution strategies: original and balanced. The original label distribution ensures that the label distribution of the synthetic graph closely matches that of the original graph, while the balanced label distribution assigns nodes uniformly to each class. Figure [6](#page-13-3) visualises different label distributions of condensed Arxiv dataset with a 90-node budget.

<span id="page-13-3"></span>Image /page/13/Figure/7 description: The image is a bar chart showing the distribution of node numbers across different classes. The x-axis represents the class, ranging from 0 to 39. The y-axis on the left represents the node number, ranging from 0 to 15. The y-axis on the right represents the ratio, ranging from 0.0 to 1.0. There are two sets of bars: 'Proportional' (light blue) and 'Balanced' (dark blue). The 'Proportional' distribution has peaks at classes 16, 28, and 34. The 'Balanced' distribution is more consistent across classes, with some peaks at classes 2, 5, 8, and 29.

Figure 6: Comparison of the class distribution between the original and balanced label initialisation strategies with respect to the number of nodes and the class ratio in each class, using a 90-node condensed Arxiv dataset as an example.

<span id="page-13-2"></span>

### A.3 Reproducibility

Condensation: Each epoch starts with the initialisation of a backbone model within a nested loop structure. The outer loop updates the condensed graph. Subsequently, the process progresses to the inner loop, where the backbone model is continuously trained using the updated condensed graph. If a structure generator (such as GCond, DosCond, SGDD, or GCDM) is employed, it is initially optimised over several epochs, followed by a few epochs dedicated to feature updates. This iterative pattern of alternating between structural and feature updates continues. For SFGC, the backbone training follows an offline style, thereby removing the need for a nested loop. Based on the

<span id="page-14-2"></span>Image /page/14/Figure/0 description: The image contains two scatter plots. The x-axis of both plots is labeled "LR for Adj" and ranges from 10^-4 to 10^0. The y-axis is labeled "LR for Feat" and also ranges from 10^-4 to 10^0. The color of the dots in the plots represents "Test Accuracy", with a color bar ranging from 0.4 to 0.6. The first plot, labeled (a) "GCond-SGC for Arxiv-90", shows a distribution of points across the LR for Adj and LR for Feat space, with the color indicating the test accuracy. The second plot, labeled (b) "SGDD-SGC for Arxiv-90", also shows a similar distribution of points with the same axes and color scheme.

Figure 7: Examples for overall experiments with Bayes hyperparameter sampler.

condensation framework and the methodologies of various methods, hyperparameter search spaces are clearly predefined in Tables [6](#page-14-0) and [7.](#page-14-1)

Validation: The validation phase employs the same model architecture as used in the condensation process (e.g., GCN or SGC). Every 10 epochs, a validator is trained from scratch using the condensed graph and assessed against the validation set of the original graph. After 200-epoch training and validating, the best validation accuracy achieved is recorded as the validation score. The condensation process is terminated early if no improvement in validation scores is observed within five validation steps (equivalent to 50 condensation epochs); otherwise, the process continues until reaching a total of 1000 epochs.

Overall test: After condensation, the optimal condensed graphs are loaded for testing. The test model remains the same as the backbone model:

- GCN: Number of layers: 2; Hidden dimension: 256; Dropout rate: 0.5
- SGC: Number of the message passing hop (k): 2

The models are trained using the condensed graphs until convergence, employing an Adam optimiser with a weight decay of 0.5 and a learning rate of 0.01. The Bayesian hyperparameter sampler quickly identifies the optimal range for the hyperparameter combination, as illustrated in Figure [7.](#page-14-2)

|                   | GCond, SGDD, GCDM     | GCondX, DosCondX      | DosCond               | DosCondX, DM          |
|-------------------|-----------------------|-----------------------|-----------------------|-----------------------|
| lr for adj        | log_uniform(1e-6,1.0) | NaN                   | log_uniform(1e-6,1.0) | NaN                   |
| lr for feat       | log_uniform(1e-6,1.0) | log_uniform(1e-6,1.0) | log_uniform(1e-6,1.0) | log_uniform(1e-6,1.0) |
| outer loop        | 5                     | 5                     | 1                     | 1                     |
| inner loop        | 10                    | 10                    | 0                     | 0                     |
| adj update steps  | 10                    | NaN                   | 10                    | NaN                   |
| feat update steps | 20                    | NaN                   | 20                    | NaN                   |

<span id="page-14-0"></span>Table 6: Predefined hyperparameter search spaces for gradient and distribution matching methods.

Table 7: Predefined hyperparameter search spaces for SFGC.

<span id="page-14-1"></span>

| lr for feat           | lr for student model  | target epochs             | warm-up epochs            | student epochs            |
|-----------------------|-----------------------|---------------------------|---------------------------|---------------------------|
| log_uniform(1e-6,1.0) | log_uniform(1e-3,1.0) | start:0, end:800, step:10 | start:0, end:100, step:10 | start:0, end:300, step:10 |

### A.4 Backbone Models

Besides GCN and SGC, which are used as condensation backbones and validators, the crossarchitecture experiment also employs other backbone models, including MLP, GAT, ChebNet, SAGE, and APPNP:

• MLP: Number of layers: 2; Hidden dimension: 256; Dropout rate: 0.5

- GAT: Number of layers: 2; Hidden dimension: 256; Number of attention heads:8; Dropout rate: 0.5; Graph sparse threshold: 0.5
- ChebNet: Number of layers: 2; Hidden dimension: 256; Dropout rate: 0.5; Graph sparse threshold: 0.5
- SAGE: Number of layers: 2; Hidden dimension: 256; Dropout rate: 0.5; Aggregator type: mean; Graph sparse threshold: 0.5
- APPNP: Number of layers: 2; Hidden dimension: 256; Dropout rate: 0.5; Number of iterations (propagation): 10; Teleport probability (propagation): 0.1

## A.5 Continual Graph Learning

Unlike the typical node classification problem, hyperparameter search in the CGL setting is challenging due to potential latency issues. This could explain why methods with fewer hyperparameters, such as DM, tend to perform better in the CGL setting. The hyperparameter choices of selected methods are shown in Table [8.](#page-15-2)

Table 8: Hyperparameter choices for CGL experiment.

<span id="page-15-2"></span>

|          | GCond                                                                     | DosCond                                            | DM                               |
|----------|---------------------------------------------------------------------------|----------------------------------------------------|----------------------------------|
| CiteSeer | GCond: lr for adj: 1e-5; lr for feat: 1e-5; outer loop: 10; inner loop: 1 | lr for adj: 1e-5; lr for feat: 1e-5; outer loop: 1 | lr for feat: 1e-4; outer loop: 1 |
| Arxiv    | lr for adj: 1e-2; lr for feat: 1e-2; outer loop: 10; inner loop: 1        | lr for adj: 1e-2; lr for feat: 1e-2; outer loop: 1 | lr for feat: 1e-3; outer loop: 1 |

# B More Experiments

In this section, more experiments are provided in addition to the ones in the main text, including the condensation efficiency and the cross-architecture transferability.

<span id="page-15-1"></span>

### B.1 Condensation Efficiency

In this experiment, more results of the condensation efficiency on all datasets are provided in Figure [8](#page-15-0) ∼ [13.](#page-18-0) Overall, distribution matching methods, such as DM, GCDM and GCDMX, can generally achieve a high performance with a high efficiency. While SFGC can sometimes obtain a higher performance, the stability and consistency are not ideal, especially given that SFGC requires an offline generation of expert trajectories.

<span id="page-15-0"></span>Image /page/15/Figure/12 description: The figure is a scatter plot showing the relationship between condensation time (in minutes) and test accuracy (in %). The x-axis represents condensation time, ranging from 0 to 35 minutes, and the y-axis represents test accuracy, ranging from 60% to 72%. Several data points are plotted, each representing a different method, with their corresponding test accuracy and condensation time. The plot uses different markers to distinguish between SGC (square) and GCN (circle) methods. The data points are labeled with abbreviations such as DM, GCDMX, DosCond, GCond, SFGC, SGDD, etc.

Figure 8: Test accuracy against condensation time for different GC methods on a 30-node condensed graph from the CiteSeer dataset, with backbone models GCN and SGC.

Image /page/16/Figure/0 description: The image is a scatter plot showing the relationship between condensation time (in minutes) and test accuracy (in %). The x-axis represents condensation time, ranging from 0 to 20 minutes, and the y-axis represents test accuracy, ranging from 76% to 80%. Several data points are plotted, each representing a different method, with their corresponding labels. The methods include: DosCond, GCondX, GCond, DM, GCDMX, SGDD, SFGC, and GCDM. The plot also includes a legend indicating the marker shapes for SGC (square) and GCN (circle).

Figure 9: Test accuracy against condensation time for different GC methods on a 35-node condensed graph from the Cora dataset, with backbone models GCN and SGC.

Image /page/16/Figure/2 description: The image is a scatter plot showing the relationship between condensation time (in minutes) and test accuracy (in percentage). The x-axis represents condensation time, ranging from 0 to 40 minutes, and the y-axis represents test accuracy, ranging from 74% to 78%. Several data points are plotted, each representing a different method, with their corresponding labels. The methods include: DM, DosCond, DosCondX, GCDM, GCond, GCondX, SFGC, SGDD, and GCDMX. The plot also includes a legend indicating the marker shapes for SGC (square) and GCN (circle).

Figure 10: Test accuracy against condensation time for different GC methods on a 15-node condensed graph from the PubMed dataset, with backbone models GCN and SGC.

<span id="page-16-0"></span>

### B.2 Cross-architecture Transferability

In this experiment, more results of the cross-architecture transferability on all datasets are demonstrated in Figure [14](#page-18-1) ∼ [19.](#page-19-0) For small-scale datasets, employing a suitable backbone model generally ensures that all baseline methods achieve comparable cross-architecture performance. However, while a condensed graph may perform well on its original architecture, its performance often deteriorates on alternative architectures when applied to large-scale datasets.

Image /page/17/Figure/0 description: The image is a scatter plot showing the relationship between "Condensation Time (min)" on the x-axis and "Test Accuracy (%)" on the y-axis. Several data points are plotted, each representing a different method, with their corresponding accuracy and condensation time. The plot includes data points labeled as SFGC, DM, GCondX, SGDD, GCond, DosCondX, DosCond, GCDMX, and GCDMX. The plot also includes a legend indicating that squares represent SGC and circles represent GCN.

Figure 11: Test accuracy against condensation time for different GC methods on a 612-node condensed graph from the Products dataset, with backbone models GCN and SGC.

Image /page/17/Figure/2 description: The image is a scatter plot showing the relationship between condensation time (in minutes) and test accuracy (in percentage) for different GC methods. The x-axis represents the condensation time, ranging from 1 to 9 minutes, and the y-axis represents the test accuracy, ranging from 34% to 46%. Several data points are plotted, each representing a different GC method. The methods are labeled as DM, DosCond, DosCondX, GCDM, GCond, GCondX, SFGC, SGDD, and GCDMX. The plot also distinguishes between two types of methods, SGC (represented by squares) and GCN (represented by circles).

Figure 12: Test accuracy against condensation time for different GC methods on a 44-node condensed graph from the Flickr dataset, with backbone models GCN and SGC.

## C Assets License

Our code base[1](#page-17-0) is open-sourced under the MIT license. All used datasets are public: CiteSeer, Cora, PubMed, Flickr and Reddit can be obtained from DGL [2](#page-17-1) or PyG [3](#page-17-2) ; ogbn-arxiv and ogbn-products are provided by OGB [4](#page-17-3) .

# D Ethic Statement

Our experiments are designed and conducted in strict adherence to ethical standards and do not involve any ethical concerns. We ensure that all procedures are compliant with relevant guidelines and that there is no harm or risk to any participants or environments involved.

<span id="page-17-0"></span><sup>1</sup> https://github.com/superallen13/GCondenser

<span id="page-17-1"></span><sup>2</sup> https://www.dgl.ai/

<span id="page-17-2"></span><sup>3</sup> https://www.pyg.org/

<span id="page-17-3"></span><sup>4</sup> https://ogb.stanford.edu/

<span id="page-18-0"></span>Image /page/18/Figure/0 description: The image is a scatter plot showing the relationship between condensation time (in minutes) and test accuracy (in percentage). The x-axis represents condensation time, ranging from 0 to 350 minutes, and the y-axis represents test accuracy, ranging from 82% to 92%. Several data points are plotted, each representing a different method, with their corresponding labels. The methods include: DM, GCDMX, DosCond, GCond, GCondX, DosCondX, GCDM, and SGDD. The plot also distinguishes between two types of methods, SGC (represented by squares) and GCN (represented by circles).

Figure 13: Test accuracy against condensation time for different GC methods on a 153-node condensed graph from the Reddit dataset, with backbone models GCN and SGC.

<span id="page-18-1"></span>

| Backbone: SGC |      |      |      |      |         |      |       |      | Backbone: GCN |      |      |      |         |      |       |      |  |
|---------------|------|------|------|------|---------|------|-------|------|---------------|------|------|------|---------|------|-------|------|--|
| GCond         | 66.4 | 71.6 | 73.2 | 61.2 | 65.9    | 72.0 | 71.0  | 68.7 | 41.8          | 34.8 | 46.3 | 39.2 | 57.4    | 61.2 | 47.0  | 46.8 |  |
| GCondX        | 67.1 | 70.0 | 72.2 | 71.8 | 62.9    | 70.9 | 72.0  | 69.6 | 60.3          | 58.4 | 65.8 | 67.9 | 60.0    | 68.3 | 67.8  | 64.1 |  |
| DosCond       | 67.9 | 71.1 | 73.4 | 62.7 | 63.0    | 67.1 | 64.7  | 67.1 | 58.7          | 46.3 | 67.3 | 62.2 | 59.0    | 65.1 | 55.6  | 59.2 |  |
| DosCondX      | 58.6 | 64.6 | 68.6 | 69.1 | 65.8    | 66.5 | 69.0  | 66.0 | 62.5          | 62.4 | 69.0 | 69.2 | 66.2    | 68.2 | 69.9  | 66.8 |  |
| SGDD          | 64.9 | 72.8 | 73.1 | 72.1 | 67.6    | 71.1 | 73.3  | 70.7 | 54.1          | 47.1 | 69.2 | 60.9 | 61.1    | 64.8 | 58.3  | 59.3 |  |
| GCDM          | 59.2 | 68.5 | 65.3 | 58.9 | 58.9    | 63.2 | 69.5  | 63.4 | 62.3          | 69.6 | 72.7 | 58.3 | 60.2    | 67.1 | 71.4  | 65.9 |  |
| GCDMX         | 59.4 | 71.3 | 66.1 | 65.9 | 58.6    | 62.5 | 66.0  | 64.3 | 65.5          | 73.9 | 72.4 | 73.4 | 66.1    | 71.1 | 73.6  | 70.8 |  |
| DM            | 64.8 | 68.6 | 71.6 | 71.3 | 66.8    | 69.7 | 72.0  | 69.3 | 66.2          | 73.6 | 72.3 | 72.7 | 65.8    | 71.9 | 73.5  | 70.8 |  |
| SFGC          | 59.2 | 63.0 | 67.6 | 69.1 | 64.6    | 67.2 | 69.2  | 65.7 | 64.4          | 64.9 | 70.4 | 70.0 | 69.1    | 69.5 | 70.8  | 68.4 |  |
|               | MLP  | SGC  | GCN  | GAT  | ChebNet | SAGE | APPNP | Avg. | MLP           | SGC  | GCN  | GAT  | ChebNet | SAGE | APPNP | Avg. |  |

Figure 14: Transferability of condensed graphs for CiteSeer with budget 30.

| Backbone: SGC |      |      |      |      |         |      |       |      |  |      |      | Backbone: GCN |      |         |      |       |      |  |  |  |
|---------------|------|------|------|------|---------|------|-------|------|--|------|------|---------------|------|---------|------|-------|------|--|--|--|
| GCond         | 75.8 | 74.1 | 76.8 | 77.0 | 76.5    | 77.0 | 79.3  | 76.6 |  | 75.1 | 55.6 | 75.0          | 77.0 | 74.3    | 77.2 | 78.0  | 73.2 |  |  |  |
| GCondX        | 76.7 | 77.7 | 78.5 | 77.7 | 74.3    | 78.1 | 79.3  | 77.5 |  | 74.8 | 78.7 | 77.4          | 77.1 | 77.1    | 75.5 | 79.6  | 77.2 |  |  |  |
| DosCond       | 76.2 | 75.0 | 76.5 | 76.5 | 73.0    | 78.0 | 78.5  | 76.2 |  | 71.8 | 58.9 | 74.5          | 74.7 | 70.2    | 75.7 | 77.5  | 71.9 |  |  |  |
| DosCondX      | 71.4 | 74.2 | 77.0 | 76.6 | 71.4    | 76.3 | 78.2  | 75.0 |  | 72.4 | 66.2 | 76.1          | 75.8 | 75.6    | 76.5 | 77.6  | 74.3 |  |  |  |
| SGDD          | 76.3 | 77.3 | 77.5 | 73.1 | 79.0    | 78.5 | 78.5  | 77.2 |  | 72.7 | 53.8 | 76.4          | 76.8 | 75.8    | 77.4 | 79.5  | 73.2 |  |  |  |
| GCDM          | 70.3 | 76.1 | 74.4 | 75.1 | 70.9    | 74.6 | 75.4  | 73.8 |  | 73.8 | 72.9 | 75.0          | 73.7 | 70.5    | 75.3 | 76.9  | 74.0 |  |  |  |
| GCDMX         | 70.5 | 74.7 | 75.4 | 75.4 | 74.4    | 74.7 | 76.6  | 74.5 |  | 72.3 | 74.9 | 77.2          | 77.0 | 71.5    | 77.0 | 78.3  | 75.5 |  |  |  |
| DM            | 69.0 | 74.0 | 76.0 | 76.1 | 73.5    | 74.2 | 77.6  | 74.4 |  | 70.7 | 73.8 | 76.0          | 76.3 | 75.0    | 75.1 | 77.2  | 74.9 |  |  |  |
| SFGC          | 72.9 | 75.7 | 76.6 | 76.7 | 72.0    | 75.6 | 76.8  | 75.2 |  | 73.6 | 76.8 | 78.5          | 76.6 | 77.2    | 76.7 | 78.9  | 76.9 |  |  |  |
|               | MLP  | SGC  | GCN  | GAT  | ChebNet | SAGE | APPNP | Avg. |  | MLP  | SGC  | GCN           | GAT  | ChebNet | SAGE | APPNP | Avg. |  |  |  |

Figure 15: Transferability of condensed graphs for PubMed with budget 15.

|          |      |      |      |      | Backbone: SGC |      |       |      |      |      |      |      | Backbone: GCN |      |       |      |    |
|----------|------|------|------|------|---------------|------|-------|------|------|------|------|------|---------------|------|-------|------|----|
|          |      |      |      |      |               |      |       |      |      |      |      |      |               |      |       |      |    |
| GCond    | 42.2 | 65.2 | 62.7 | 51.0 | 46.2          | 44.2 | 60.2  | 53.1 | 39.2 | 58.0 | 57.0 | 47.7 | 36.4          | 33.5 | 54.3  | 46.6 | 65 |
| GCondX   | 41.7 | 65.8 | 60.4 | 60.1 | 50.6          | 58.0 | 57.2  | 56.3 | 41.8 | 61.7 | 62.4 | 62.3 | 51.1          | 57.5 | 61.1  | 56.8 | 60 |
| DosCond  | 41.9 | 64.3 | 60.6 | 53.7 | 45.3          | 48.1 | 58.5  | 53.2 | 41.2 | 60.1 | 59.6 | 46.9 | 37.9          | 36.3 | 58.1  | 48.6 |    |
| DosCondX | 41.6 | 64.0 | 63.9 | 62.2 | 53.4          | 58.5 | 63.4  | 58.2 | 39.3 | 59.6 | 61.5 | 58.6 | 47.0          | 55.8 | 61.1  | 54.7 | 55 |
| SGDD     | 42.1 | 64.5 | 60.7 | 51.8 | 49.0          | 50.8 | 60.9  | 54.3 | 38.7 | 57.9 | 57.9 | 46.2 | 45.7          | 47.8 | 55.6  | 50.0 | 50 |
| GCDM     | 40.7 | 59.1 | 56.3 | 49.6 | 45.0          | 47.0 | 55.5  | 50.5 | 41.6 | 59.8 | 60.7 | 46.5 | 52.6          | 55.3 | 60.3  | 53.8 | 45 |
| GCDMX    | 39.8 | 60.9 | 56.2 | 55.7 | 50.3          | 55.3 | 59.6  | 54.0 | 43.5 | 63.3 | 63.9 | 62.2 | 55.6          | 59.6 | 62.8  | 58.7 |    |
| DM       | 40.9 | 62.1 | 56.4 | 55.4 | 45.4          | 55.1 | 61.0  | 53.7 | 44.1 | 62.0 | 63.8 | 62.6 | 55.2          | 59.2 | 63.3  | 58.6 | 40 |
| SFGC     | 37.0 | 66.2 | 55.5 | 49.2 | 33.7          | 43.3 | 48.4  | 47.6 | 45.3 | 62.2 | 63.3 | 60.5 | 50.7          | 55.4 | 62.4  | 57.1 | 35 |
|          | MLP  | SGC  | GCN  | GAT  | ChebNet       | SAGE | APPNP | Avg. | MLP  | SGC  | GCN  | GAT  | ChebNet       | SAGE | APPNP | Avg. |    |

Figure 16: Transferability of condensed graphs for Arxiv with budget 90.

|          |      |      |      |      | Backbone: SGC |      |       |      |      |      |      |      | Backbone: GCN |      |       |      |    |
|----------|------|------|------|------|---------------|------|-------|------|------|------|------|------|---------------|------|-------|------|----|
| GCond    | 46.9 | 64.1 | 66.9 | 63.4 | 56.9          | 57.5 | 68.6  | 60.6 | 36.4 | 45.7 | 60.7 | 48.4 | 45.2          | 49.8 | 60.3  | 49.5 |    |
| GCondX   | 46.9 | 64.9 | 68.1 | 69.3 | 60.6          | 64.7 | 64.5  | 62.7 | 43.0 | 61.7 | 64.0 | 65.1 | 56.4          | 59.8 | 64.2  | 59.2 | 65 |
| DosCond  | 43.3 | 62.1 | 63.3 | 54.4 | 48.0          | 50.4 | 64.6  | 55.1 | 41.4 | 51.1 | 60.0 | 53.6 | 46.6          | 49.7 | 62.3  | 52.1 | 60 |
| DosCondX | 40.6 | 60.4 | 58.5 | 60.6 | 56.0          | 59.5 | 57.1  | 56.1 | 43.2 | 57.6 | 60.5 | 62.9 | 56.1          | 59.5 | 60.9  | 57.3 | 55 |
| SGDD     | 44.5 | 64.9 | 66.8 | 68.9 | 60.3          | 64.1 | 65.6  | 62.1 | 42.6 | 55.3 | 62.3 | 51.5 | 50.1          | 52.9 | 64.7  | 54.2 |    |
| GCDM     | 44.6 | 56.1 | 61.3 | 63.1 | 56.3          | 58.8 | 63.3  | 57.6 | 45.7 | 60.0 | 66.6 | 67.9 | 61.2          | 63.6 | 66.2  | 61.6 | 50 |
| GCDMX    | 46.1 | 57.9 | 61.8 | 63.1 | 56.8          | 59.2 | 62.2  | 58.1 | 46.8 | 60.3 | 67.6 | 69.2 | 62.2          | 65.3 | 64.4  | 62.3 | 45 |
| DM       | 46.1 | 57.9 | 62.5 | 63.3 | 57.5          | 59.9 | 62.2  | 58.5 | 46.4 | 57.9 | 66.0 | 67.5 | 60.6          | 64.0 | 64.8  | 61.0 | 40 |
| SFGC     | 34.5 | 62.3 | 48.9 | 59.1 | 46.3          | 54.8 | 61.8  | 52.5 | 46.7 | 55.1 | 66.7 | 69.4 | 61.4          | 63.4 | 64.8  | 61.1 | 35 |
|          | MLP  | SGC  | GCN  | GAT  | ChebNet       | SAGE | APPNP | Avg. | MLP  | SGC  | GCN  | GAT  | ChebNet       | SAGE | APPNP | Avg. |    |

Figure 17: Transferability of condensed graphs for Products with budget 612.

|          |      |      |      |      | Backbone: SGC |      |       |      |      |      |      |      | Backbone: GCN |      |       |      |      |
|----------|------|------|------|------|---------------|------|-------|------|------|------|------|------|---------------|------|-------|------|------|
| GCond    | 42.5 | 40.0 | 44.0 | 42.3 | 38.4          | 43.2 | 44.7  | 42.2 | 40.8 | 36.5 | 44.9 | 40.8 | 43.0          | 43.2 | 44.9  | 42.0 | 45.0 |
| GCondX   | 43.5 | 41.6 | 44.8 | 43.1 | 30.6          | 43.5 | 45.4  | 41.8 | 43.8 | 38.3 | 45.1 | 43.5 | 28.3          | 43.8 | 44.9  | 41.1 | 42.5 |
| DosCond  | 43.7 | 38.9 | 44.7 | 41.4 | 43.8          | 44.2 | 45.3  | 43.2 | 42.1 | 28.3 | 41.4 | 38.8 | 28.9          | 41.6 | 45.6  | 38.1 | 40.0 |
| DosCondX | 40.2 | 39.7 | 42.9 | 42.0 | 40.5          | 40.6 | 44.8  | 41.5 | 44.2 | 43.5 | 45.7 | 44.4 | 33.5          | 44.4 | 46.0  | 43.1 | 37.5 |
| SGDD     | 42.9 | 36.0 | 43.7 | 40.4 | 40.5          | 40.9 | 45.5  | 41.4 | 42.3 | 35.7 | 44.7 | 41.3 | 41.1          | 41.2 | 44.9  | 41.6 | 35.0 |
| GCDM     | 42.3 | 31.3 | 37.4 | 40.8 | 36.4          | 39.7 | 44.9  | 39.0 | 41.7 | 27.3 | 40.7 | 37.7 | 41.5          | 43.0 | 43.8  | 39.4 | 32.5 |
| GCDMX    | 42.3 | 41.5 | 44.2 | 42.0 | 36.4          | 42.9 | 44.6  | 42.0 | 43.9 | 39.4 | 45.5 | 44.1 | 31.5          | 44.0 | 46.0  | 42.1 | 30.0 |
| DM       | 43.4 | 43.8 | 45.3 | 44.1 | 41.0          | 43.6 | 45.5  | 43.8 | 44.2 | 40.1 | 45.9 | 44.8 | 43.7          | 44.0 | 45.9  | 44.1 | 27.5 |
| SFGC     | 38.6 | 36.2 | 44.8 | 43.6 | 40.3          | 44.4 | 44.9  | 41.8 | 44.9 | 38.7 | 46.2 | 45.3 | 43.6          | 44.9 | 46.2  | 44.3 |      |
|          | MLP  | SGC  | GCN  | GAT  | ChebNet       | SAGE | APPNP | Avg. | MLP  | SGC  | GCN  | GAT  | ChebNet       | SAGE | APPNP | Avg. |      |

Figure 18: Transferability of condensed graphs for Flickr with budget 44.

<span id="page-19-0"></span>

|          |      |      |      |      | Backbone: SGC |      |       |      |      |      |      |      | Backbone: GCN |      |       |      |  |
|----------|------|------|------|------|---------------|------|-------|------|------|------|------|------|---------------|------|-------|------|--|
|          |      |      |      |      |               |      |       |      |      |      |      |      |               |      |       |      |  |
| GCond    | 43.0 | 90.3 | 85.7 | 31.1 | 48.2          | 52.5 | 76.3  | 61.0 | 38.7 | 82.2 | 79.9 | 31.2 | 38.7          | 41.5 | 69.8  | 54.6 |  |
| GCondX   | 41.8 | 91.3 | 91.2 | 89.8 | 64.6          | 79.7 | 74.6  | 76.1 | 43.0 | 86.6 | 87.2 | 86.9 | 54.4          | 74.2 | 63.2  | 70.8 |  |
| DosCond  | 41.5 | 91.5 | 90.5 | 36.1 | 46.3          | 62.5 | 78.3  | 63.8 | 41.7 | 88.4 | 87.2 | 28.1 | 40.1          | 45.0 | 72.5  | 57.6 |  |
| DosCondX | 42.2 | 90.7 | 89.7 | 89.5 | 60.8          | 77.7 | 66.6  | 73.9 | 42.5 | 89.9 | 88.7 | 88.9 | 48.8          | 75.8 | 66.0  | 71.5 |  |
| SGDD     | 42.0 | 90.1 | 88.3 | 31.8 | 47.2          | 47.7 | 75.5  | 60.4 | 39.7 | 82.6 | 81.5 | 30.3 | 39.5          | 42.4 | 70.3  | 55.2 |  |
| GCDM     | 42.1 | 90.5 | 85.4 | 32.6 | 50.6          | 63.8 | 75.8  | 63.0 | 43.1 | 87.1 | 88.1 | 37.5 | 55.6          | 66.2 | 68.9  | 63.8 |  |
| GCDMX    | 37.8 | 92.0 | 56.0 | 60.6 | 27.5          | 59.9 | 54.1  | 55.4 | 42.6 | 90.9 | 91.4 | 89.1 | 69.9          | 79.6 | 77.2  | 77.2 |  |
| DM       | 42.9 | 89.7 | 91.1 | 88.7 | 66.4          | 79.5 | 78.7  | 76.7 | 42.1 | 90.0 | 91.2 | 87.9 | 69.4          | 80.4 | 75.3  | 76.6 |  |
| SFGC     | 33.9 | 91.2 | 75.3 | 64.9 | 28.9          | 43.3 | 38.4  | 53.7 | 47.5 | 82.8 | 87.0 | 84.4 | 53.6          | 71.9 | 67.5  | 70.7 |  |
|          | MLP  | SGC  | GCN  | GAT  | ChebNet       | SAGE | APPNP | Avg. | MLP  | SGC  | GCN  | GAT  | ChebNet       | SAGE | APPNP | Avg. |  |

Figure 19: Transferability of condensed graphs for Reddit with budget 153.