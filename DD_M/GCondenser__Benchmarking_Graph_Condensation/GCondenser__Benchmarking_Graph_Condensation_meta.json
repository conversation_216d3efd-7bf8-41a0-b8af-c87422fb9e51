{"table_of_contents": [{"title": "GCondenser: Benchmarking Graph Condensation", "heading_level": null, "page_id": 0, "polygon": [[119.830078125, 99.8338623046875], [492.767578125, 99.8338623046875], [492.767578125, 117.04925537109375], [119.830078125, 117.04925537109375]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[282.2431640625, 222.63116455078125], [328.2432861328125, 222.63116455078125], [328.2432861328125, 234.58636474609375], [282.2431640625, 234.58636474609375]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[108.00001525878906, 423.1021728515625], [191.6982421875, 423.1021728515625], [191.6982421875, 435.057373046875], [108.00001525878906, 435.057373046875]]}, {"title": "2 Preliminary", "heading_level": null, "page_id": 1, "polygon": [[106.90576171875, 269.71612548828125], [187.47817993164062, 269.71612548828125], [187.47817993164062, 281.67132568359375], [106.90576171875, 281.67132568359375]]}, {"title": "3 Benchmark Design", "heading_level": null, "page_id": 2, "polygon": [[106.98046875, 598.7791442871094], [223.822265625, 598.7791442871094], [223.822265625, 610.7343444824219], [106.98046875, 610.7343444824219]]}, {"title": "3.1 Graph Condensation Modules", "heading_level": null, "page_id": 2, "polygon": [[106.8310546875, 659.478515625], [258.8437194824219, 659.478515625], [258.8437194824219, 669.4411163330078], [106.8310546875, 669.4411163330078]]}, {"title": "3.2 Evaluations", "heading_level": null, "page_id": 3, "polygon": [[108.0, 191.5224609375], [181.5380859375, 191.5224609375], [181.5380859375, 202.080078125], [108.0, 202.080078125]]}, {"title": "4 Experiments", "heading_level": null, "page_id": 3, "polygon": [[107.4287109375, 610.9921569824219], [191.6982421875, 610.9921569824219], [191.6982421875, 622.9473571777344], [107.4287109375, 622.9473571777344]]}, {"title": "4.1 Baselines", "heading_level": null, "page_id": 4, "polygon": [[107.50341796875, 110.8916015625], [170.630859375, 110.8916015625], [170.630859375, 121.58612060546875], [107.50341796875, 121.58612060546875]]}, {"title": "4.2 Implementations", "heading_level": null, "page_id": 4, "polygon": [[107.95166015625, 398.3203125], [203.80078125, 398.3203125], [203.80078125, 408.8360900878906], [107.95166015625, 408.8360900878906]]}, {"title": "4.3 Overall Results (RQ1)", "heading_level": null, "page_id": 4, "polygon": [[107.578125, 565.76953125], [227.109375, 565.76953125], [227.109375, 576.0850982666016], [107.578125, 576.0850982666016]]}, {"title": "4.4 Condensation Efficiency (RQ2)", "heading_level": null, "page_id": 5, "polygon": [[107.35400390625, 619.91015625], [264.76171875, 619.91015625], [264.76171875, 630.8951110839844], [107.35400390625, 630.8951110839844]]}, {"title": "4.6 Impact of Different Validators (RQ4)", "heading_level": null, "page_id": 6, "polygon": [[107.4287109375, 670.18359375], [288.13372802734375, 670.18359375], [288.13372802734375, 680.5231094360352], [107.4287109375, 680.5231094360352]]}, {"title": "4.7 Cross-architecture Transferability (RQ5)", "heading_level": null, "page_id": 7, "polygon": [[106.90576171875, 272.6314697265625], [305.701171875, 272.6314697265625], [305.701171875, 282.59405517578125], [106.90576171875, 282.59405517578125]]}, {"title": "4.8 Continual Graph Learning (RQ6)", "heading_level": null, "page_id": 7, "polygon": [[106.45751953125, 561.4545135498047], [275.818359375, 561.4545135498047], [275.818359375, 571.4171142578125], [106.45751953125, 571.4171142578125]]}, {"title": "5 Related Work", "heading_level": null, "page_id": 8, "polygon": [[107.279296875, 516.6171875], [197.3759765625, 516.6171875], [197.3759765625, 528.5723876953125], [107.279296875, 528.5723876953125]]}, {"title": "6 Conclusion and Future Work", "heading_level": null, "page_id": 9, "polygon": [[107.1298828125, 112.3311767578125], [275.9677734375, 112.3311767578125], [275.9677734375, 124.286376953125], [107.1298828125, 124.286376953125]]}, {"title": "References", "heading_level": null, "page_id": 9, "polygon": [[107.65283203125, 349.73016357421875], [164.056640625, 349.73016357421875], [164.056640625, 361.68536376953125], [107.65283203125, 361.68536376953125]]}, {"title": "Checklist", "heading_level": null, "page_id": 12, "polygon": [[107.876953125, 72.4130859375], [157.3330078125, 72.4130859375], [157.3330078125, 84.7423095703125], [107.876953125, 84.7423095703125]]}, {"title": "A Implementation Details", "heading_level": null, "page_id": 13, "polygon": [[106.5322265625, 72.26806640625], [249.6708984375, 72.26806640625], [249.6708984375, 84.7423095703125], [106.5322265625, 84.7423095703125]]}, {"title": "A.1 Graph Condensation Settings", "heading_level": null, "page_id": 13, "polygon": [[106.681640625, 96.82470703125], [259.083984375, 96.82470703125], [259.083984375, 107.6060791015625], [106.681640625, 107.6060791015625]]}, {"title": "A.2 Label Distribution", "heading_level": null, "page_id": 13, "polygon": [[107.35400390625, 368.736328125], [212.466796875, 368.736328125], [212.466796875, 379.12408447265625], [107.35400390625, 379.12408447265625]]}, {"title": "A.3 Reproducibility", "heading_level": null, "page_id": 13, "polygon": [[107.279296875, 626.7815093994141], [199.916015625, 626.7815093994141], [199.916015625, 636.7441101074219], [107.279296875, 636.7441101074219]]}, {"title": "A.4 Backbone Models", "heading_level": null, "page_id": 14, "polygon": [[106.98046875, 650.6455230712891], [209.1796875, 650.6455230712891], [209.1796875, 660.90234375], [106.98046875, 660.90234375]]}, {"title": "A.5 Continual Graph Learning", "heading_level": null, "page_id": 15, "polygon": [[107.7275390625, 188.33203125], [248.4755859375, 188.33203125], [248.4755859375, 198.9901123046875], [107.7275390625, 198.9901123046875]]}, {"title": "B More Experiments", "heading_level": null, "page_id": 15, "polygon": [[106.98046875, 341.47265625], [225.615234375, 341.47265625], [225.615234375, 354.0953674316406], [106.98046875, 354.0953674316406]]}, {"title": "B.1 Condensation Efficiency", "heading_level": null, "page_id": 15, "polygon": [[106.98046875, 402.9609375], [237.26953125, 402.9609375], [237.26953125, 413.015625], [106.98046875, 413.015625]]}, {"title": "B.2 Cross-architecture Transferability", "heading_level": null, "page_id": 16, "polygon": [[107.05517578125, 629.3695068359375], [277.7228698730469, 629.3695068359375], [277.7228698730469, 639.3321075439453], [107.05517578125, 639.3321075439453]]}, {"title": "C Assets License", "heading_level": null, "page_id": 17, "polygon": [[106.90576171875, 541.7051544189453], [202.0078125, 541.7051544189453], [202.0078125, 553.6603546142578], [106.90576171875, 553.6603546142578]]}, {"title": "D Ethic Statement", "heading_level": null, "page_id": 17, "polygon": [[107.279296875, 614.49609375], [211.5703125, 614.49609375], [211.5703125, 626.**********], [107.279296875, 626.**********]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 152], ["Line", 45], ["Text", 6], ["SectionHeader", 3], ["ListItem", 1], ["<PERSON>Footer", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 9741, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 645], ["Line", 131], ["TextInlineMath", 8], ["ListItem", 5], ["Equation", 4], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 539], ["Line", 139], ["Text", 5], ["Equation", 5], ["TextInlineMath", 3], ["Caption", 2], ["SectionHeader", 2], ["Figure", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 1299, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 164], ["TableCell", 99], ["Line", 59], ["Text", 16], ["SectionHeader", 2], ["Table", 2], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 1, "llm_tokens_used": 5717, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 174], ["Line", 49], ["Text", 13], ["SectionHeader", 3], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 2406], ["TableCell", 697], ["Line", 94], ["Text", 6], ["Caption", 1], ["Table", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 187], ["TableCell", 120], ["Line", 80], ["Text", 7], ["Reference", 5], ["Table", 3], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 1, "llm_tokens_used": 7769, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 477], ["TableCell", 88], ["Line", 79], ["Reference", 4], ["Text", 3], ["Caption", 2], ["SectionHeader", 2], ["Table", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5635, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 349], ["Line", 61], ["Text", 3], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2290, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 189], ["Line", 46], ["Reference", 13], ["ListItem", 12], ["Text", 4], ["SectionHeader", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 193], ["Line", 44], ["ListItem", 22], ["Reference", 22], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 19], ["Line", 5], ["ListItem", 2], ["Reference", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 157], ["Line", 36], ["ListItem", 23], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 223], ["Line", 47], ["Reference", 5], ["SectionHeader", 4], ["Text", 3], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 3745, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 244], ["Line", 56], ["TableCell", 45], ["Text", 6], ["Caption", 3], ["ListItem", 3], ["Reference", 3], ["Table", 2], ["TableGroup", 2], ["Figure", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 4613, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 165], ["Line", 59], ["TableCell", 12], ["ListItem", 4], ["SectionHeader", 3], ["Text", 3], ["Reference", 3], ["Caption", 2], ["Table", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 6741, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 162], ["Line", 63], ["Figure", 2], ["Caption", 2], ["FigureGroup", 2], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5830, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 219], ["Line", 81], ["Footnote", 4], ["Reference", 4], ["Figure", 2], ["Caption", 2], ["SectionHeader", 2], ["FigureGroup", 2], ["TextInlineMath", 1], ["Text", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4745, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 790], ["TableCell", 754], ["Line", 106], ["Caption", 3], ["Table", 2], ["TableGroup", 2], ["Reference", 2], ["Figure", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 15318, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 1481], ["Span", 1451], ["Line", 158], ["Table", 4], ["Caption", 4], ["TableGroup", 4], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 4, "llm_error_count": 0, "llm_tokens_used": 27826, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/GCondenser__Benchmarking_Graph_Condensation"}