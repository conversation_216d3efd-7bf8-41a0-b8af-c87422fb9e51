{"table_of_contents": [{"title": "FedLAP-DP: Federated Learning by Sharing Differentially Private\nLoss Approximations", "heading_level": null, "page_id": 0, "polygon": [[53.79800033569336, 82.76910400390625], [558.509765625, 82.76910400390625], [558.509765625, 119.90948486328125], [53.79800033569336, 119.90948486328125]]}, {"title": "ABSTRACT", "heading_level": null, "page_id": 0, "polygon": [[53.7979736328125, 245.52264404296875], [112.060546875, 245.52264404296875], [112.060546875, 256.4317626953125], [53.7979736328125, 256.4317626953125]]}, {"title": "KEYWORDS", "heading_level": null, "page_id": 0, "polygon": [[52.257568359375, 522.4776611328125], [115.66349792480469, 522.4776611328125], [115.66349792480469, 533.3867492675781], [52.257568359375, 533.3867492675781]]}, {"title": "1 INTRODUCTION", "heading_level": null, "page_id": 0, "polygon": [[53.79800033569336, 560.6116638183594], [156.5290069580078, 560.6116638183594], [156.5290069580078, 571.5207672119141], [53.79800033569336, 571.5207672119141]]}, {"title": "2 RELATED WORK", "heading_level": null, "page_id": 1, "polygon": [[53.797996520996094, 633.8226623535156], [157.9306640625, 633.8226623535156], [157.9306640625, 645.046875], [53.797996520996094, 645.046875]]}, {"title": "3 BACKGROUND", "heading_level": null, "page_id": 2, "polygon": [[316.7578125, 637.0606689453125], [412.60235595703125, 637.0606689453125], [412.60235595703125, 647.9697723388672], [316.7578125, 647.9697723388672]]}, {"title": "3.1 Federated Learning", "heading_level": null, "page_id": 2, "polygon": [[317.953125, 652.7516784667969], [439.27734375, 652.7516784667969], [439.27734375, 663.6607818603516], [317.953125, 663.6607818603516]]}, {"title": "3.2 Non-IID Challenges", "heading_level": null, "page_id": 3, "polygon": [[53.41552734375, 597.9136657714844], [176.830810546875, 597.9136657714844], [176.830810546875, 608.8227691650391], [53.41552734375, 608.8227691650391]]}, {"title": "3.3 Differential Privacy", "heading_level": null, "page_id": 3, "polygon": [[316.7578125, 286.2807312011719], [440.4859924316406, 286.2807312011719], [440.4859924316406, 297.1898193359375], [316.7578125, 297.1898193359375]]}, {"title": "Algorithm 1 FedLAP: Local Approximation", "heading_level": null, "page_id": 4, "polygon": [[52.96728515625, 86.1453857421875], [216.21543884277344, 86.1453857421875], [216.21543884277344, 95.24932861328125], [52.96728515625, 95.24932861328125]]}, {"title": "4 FEDLAP-DP", "heading_level": null, "page_id": 4, "polygon": [[53.602294921875, 571.18359375], [130.58714294433594, 571.18359375], [130.58714294433594, 582.2167816162109], [53.602294921875, 582.2167816162109]]}, {"title": "4.1 Overview", "heading_level": null, "page_id": 4, "polygon": [[53.3408203125, 586.9986724853516], [125.52532958984375, 586.9986724853516], [125.52532958984375, 597.9077758789062], [53.3408203125, 597.9077758789062]]}, {"title": "4.2 Local Approximation", "heading_level": null, "page_id": 4, "polygon": [[316.7578125, 619.7796783447266], [448.3296203613281, 619.7796783447266], [448.3296203613281, 630.6887817382812], [316.7578125, 630.6887817382812]]}, {"title": "4.3 Global Optimization", "heading_level": null, "page_id": 5, "polygon": [[317.35546875, 517.2036437988281], [443.6932678222656, 517.2036437988281], [443.6932678222656, 528.1127319335938], [317.35546875, 528.1127319335938]]}, {"title": "Algorithm 2 FedLAP: Global Optimization", "heading_level": null, "page_id": 6, "polygon": [[53.11669921875, 86.1453857421875], [212.87095642089844, 86.1453857421875], [212.87095642089844, 95.24932861328125], [53.11669921875, 95.24932861328125]]}, {"title": "4.4 Record-level DP", "heading_level": null, "page_id": 6, "polygon": [[53.7890625, 315.94921875], [158.5283203125, 315.94921875], [158.5283203125, 327.0387878417969], [53.7890625, 327.0387878417969]]}, {"title": "5 PRIVACY ANALYSIS", "heading_level": null, "page_id": 7, "polygon": [[52.705810546875, 249.38165283203125], [172.8720703125, 249.38165283203125], [172.8720703125, 260.290771484375], [52.705810546875, 260.290771484375]]}, {"title": "5.1 Definitions", "heading_level": null, "page_id": 7, "polygon": [[53.378173828125, 265.07366943359375], [133.95806884765625, 265.07366943359375], [133.95806884765625, 275.9827880859375], [53.378173828125, 275.9827880859375]]}, {"title": "5.2 Analysis", "heading_level": null, "page_id": 7, "polygon": [[53.56494140625, 641.7926788330078], [121.54833984375, 641.7926788330078], [121.54833984375, 652.7017822265625], [53.56494140625, 652.7017822265625]]}, {"title": "6 EXPERIMENTS", "heading_level": null, "page_id": 8, "polygon": [[53.71435546875, 618.1316833496094], [149.18994140625, 618.1316833496094], [149.18994140625, 629.0407867431641], [53.71435546875, 629.0407867431641]]}, {"title": "6.1 Setup", "heading_level": null, "page_id": 8, "polygon": [[53.79800033569336, 633.8226776123047], [106.681640625, 633.8226776123047], [106.681640625, 644.7317810058594], [53.79800033569336, 644.7317810058594]]}, {"title": "6.2 Data Heterogeneity", "heading_level": null, "page_id": 9, "polygon": [[53.303466796875, 444.33984375], [175.5615234375, 444.33984375], [175.5615234375, 455.4417724609375], [53.303466796875, 455.4417724609375]]}, {"title": "6.3 Privacy Protection", "heading_level": null, "page_id": 9, "polygon": [[316.16015625, 386.00372314453125], [434.4314270019531, 386.00372314453125], [434.4314270019531, 396.9128112792969], [316.16015625, 396.9128112792969]]}, {"title": "6.4 Ablation Study", "heading_level": null, "page_id": 9, "polygon": [[317.9549865722656, 633.8226776123047], [417.1640625, 633.8226776123047], [417.1640625, 644.7317810058594], [317.9549865722656, 644.7317810058594]]}, {"title": "6.5 Qualitative Results", "heading_level": null, "page_id": 10, "polygon": [[53.71435546875, 652.7516632080078], [173.619140625, 652.7516632080078], [173.619140625, 663.6607666015625], [53.71435546875, 663.6607666015625]]}, {"title": "6.6 Privacy Auditing", "heading_level": null, "page_id": 10, "polygon": [[317.35546875, 276.1636962890625], [426.7265625, 276.1636962890625], [426.7265625, 287.07275390625], [317.35546875, 287.07275390625]]}, {"title": "7 DISCUSSION", "heading_level": null, "page_id": 10, "polygon": [[317.9549865722656, 611.9046630859375], [398.7041320800781, 611.9046630859375], [398.7041320800781, 622.8137664794922], [317.9549865722656, 622.8137664794922]]}, {"title": "8 CONCLUSION", "heading_level": null, "page_id": 12, "polygon": [[53.228759765625, 532.2036743164062], [141.64453125, 532.2036743164062], [141.64453125, 543.1127624511719], [53.228759765625, 543.1127624511719]]}, {"title": "ACKNOWLEDGMENTS", "heading_level": null, "page_id": 12, "polygon": [[317.35546875, 401.86669921875], [435.5114440917969, 401.86669921875], [435.5114440917969, 412.7757873535156], [317.35546875, 412.7757873535156]]}, {"title": "REFERENCES", "heading_level": null, "page_id": 12, "polygon": [[317.953125, 600.9456634521484], [387.3695983886719, 600.9456634521484], [387.3695983886719, 611.8547668457031], [317.953125, 611.8547668457031]]}, {"title": "APPENDICES", "heading_level": null, "page_id": 15, "polygon": [[53.079345703125, 85.62872314453125], [121.83805847167969, 85.62872314453125], [121.83805847167969, 96.537841796875], [53.079345703125, 96.537841796875]]}, {"title": "A ADDITIONAL ANALYSIS", "heading_level": null, "page_id": 15, "polygon": [[53.154052734375, 110.7987060546875], [197.30718994140625, 110.7987060546875], [197.30718994140625, 121.70782470703125], [53.154052734375, 121.70782470703125]]}, {"title": "A.1 Matching Criteria", "heading_level": null, "page_id": 15, "polygon": [[53.11669921875, 126.48968505859375], [169.4781036376953, 126.48968505859375], [169.4781036376953, 137.3988037109375], [53.11669921875, 137.3988037109375]]}, {"title": "A.2 Radius Selection", "heading_level": null, "page_id": 15, "polygon": [[317.056640625, 310.36669921875], [427.623046875, 310.36669921875], [427.623046875, 321.2757873535156], [317.056640625, 321.2757873535156]]}, {"title": "A.3 Qualitative Results", "heading_level": null, "page_id": 15, "polygon": [[317.35546875, 641.7926788330078], [439.296875, 641.7926788330078], [439.296875, 652.7017822265625], [317.35546875, 652.7017822265625]]}, {"title": "Figure 10: Architecture of ConvNets used in the federated experiments.", "heading_level": null, "page_id": 16, "polygon": [[161.068359375, 272.25], [450.3599548339844, 272.25], [450.3599548339844, 281.72637939453125], [161.068359375, 281.72637939453125]]}, {"title": "B COMPUTATION COMPLEXITY", "heading_level": null, "page_id": 16, "polygon": [[317.654296875, 652.7516784667969], [492.4678649902344, 652.7516784667969], [492.4678649902344, 663.6607818603516], [317.654296875, 664.3828125]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 254], ["Line", 102], ["Text", 12], ["SectionHeader", 4], ["Footnote", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 252], ["Line", 110], ["Text", 8], ["ListItem", 4], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 197], ["Line", 72], ["Text", 6], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 757], ["Line", 212], ["Text", 11], ["TextInlineMath", 9], ["Equation", 5], ["Reference", 3], ["SectionHeader", 2], ["<PERSON>Footer", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 674], ["Line", 180], ["Text", 10], ["Reference", 7], ["TextInlineMath", 6], ["SectionHeader", 4], ["Equation", 4], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 2, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 745], ["Line", 234], ["Equation", 7], ["TextInlineMath", 7], ["Reference", 6], ["Text", 5], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 2, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 965], ["Line", 309], ["Text", 5], ["TextInlineMath", 5], ["Reference", 3], ["SectionHeader", 2], ["Equation", 2], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 2, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 922], ["Line", 187], ["TextInlineMath", 17], ["Reference", 9], ["Text", 6], ["Equation", 6], ["SectionHeader", 3], ["<PERSON>Footer", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 450], ["Line", 91], ["TableCell", 32], ["Text", 6], ["Reference", 4], ["Caption", 2], ["TextInlineMath", 2], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Figure", 1], ["Equation", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 2, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 319], ["Line", 69], ["TableCell", 40], ["Text", 5], ["Reference", 4], ["SectionHeader", 3], ["Caption", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Figure", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 2, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 273], ["Line", 105], ["TableCell", 27], ["Text", 9], ["SectionHeader", 3], ["Reference", 3], ["Table", 2], ["Caption", 2], ["TableGroup", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 2, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 128], ["Line", 39], ["Figure", 4], ["Caption", 3], ["Text", 3], ["FigureGroup", 3], ["Reference", 3], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 4, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 170], ["Line", 63], ["Reference", 6], ["Text", 4], ["ListItem", 4], ["Caption", 3], ["SectionHeader", 3], ["Figure", 2], ["FigureGroup", 2], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 2, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 562], ["Line", 157], ["ListItem", 49], ["Reference", 49], ["ListGroup", 2], ["Text", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 127], ["Line", 36], ["ListItem", 11], ["Reference", 11], ["Text", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 406], ["Line", 119], ["Text", 11], ["TableCell", 8], ["SectionHeader", 5], ["Reference", 3], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Equation", 2], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 89], ["Line", 41], ["Text", 4], ["SectionHeader", 2], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Code", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 161], ["Line", 50], ["Text", 5], ["Figure", 4], ["Caption", 4], ["FigureGroup", 4], ["Reference", 4], ["Equation", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 6, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 9], ["Line", 5], ["Text", 2], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 2, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/FedLAP-DP__Federated_Learning_by_Sharing_Differentially_Private_Loss_Approximations"}