# Dataset Distillation with Infinitely Wide Convolutional Networks

<PERSON>† <sup>∗</sup> Roman Novak♠

<PERSON><PERSON><PERSON>♠

<PERSON><PERSON><PERSON><PERSON>♠

<PERSON>Mind† Google Research, Brain Team♠ timothyc<PERSON><EMAIL> {romann, xlc, j<PERSON><PERSON>ee}@google.com

## Abstract

The effectiveness of machine learning algorithms arises from being able to extract useful features from large amounts of data. As model and dataset sizes increase, dataset distillation methods that compress large datasets into significantly smaller yet highly performant ones will become valuable in terms of training efficiency and useful feature extraction. To that end, we apply a novel distributed kernel-based meta-learning framework to achieve state-of-the-art results for dataset distillation using infinitely wide convolutional neural networks. For instance, using only 10 datapoints (0.02% of original dataset), we obtain over 65% test accuracy on CIFAR-10 image classification task, a dramatic improvement over the previous best test accuracy of 40%. Our state-of-the-art results extend across many other settings for MNIST, Fashion-MNIST, CIFAR-10, CIFAR-100, and SVHN. Furthermore, we perform some preliminary analyses of our distilled datasets to shed light on how they differ from naturally occurring data.

# 1 Introduction

Deep learning has become extraordinarily successful in a wide variety of settings through the availability of large datasets [\[<PERSON><PERSON><PERSON><PERSON> et al.,](#page-10-0) [2012,](#page-10-0) [<PERSON> et al.,](#page-10-1) [2018,](#page-10-1) [Brown et al.,](#page-10-2) [2020,](#page-10-2) [Dosovitskiy et al.,](#page-10-3) [2020\]](#page-10-3). Such large datasets enable a neural network to learn useful representations of the data that are adapted to solving tasks of interest. Unfortunately, it can be prohibitively costly to acquire such large datasets and train a neural network for the requisite amount of time.

One way to mitigate this problem is by constructing smaller datasets that are nevertheless informative. Some direct approaches to this include choosing a representative subset of the dataset (i.e. a coreset) or else performing a low-dimensional projection that reduces the number of features. However, such methods typically introduce a tradeoff between performance and dataset size, since what they produce is a coarse approximation of the full dataset. By contrast, the approach of *dataset distillation* is to synthesize datasets that are *more* informative than their natural counterparts when equalizing for dataset size [\[Wang et al.,](#page-10-4) [2018,](#page-10-4) [Bohdal et al.,](#page-10-5) [2020,](#page-10-5) [Nguyen et al.,](#page-10-6) [2021,](#page-10-6) [Zhao and Bilen,](#page-10-7) [2021\]](#page-10-7). Such resulting datasets will not arise from the distribution of natural images but will nevertheless capture features useful to a neural network, a capability which remains mysterious and is far from being well-understood [\[Ilyas et al.,](#page-10-8) [2019,](#page-10-8) [Huh et al.,](#page-10-9) [2016,](#page-10-9) [Hermann and Lampinen,](#page-10-10) [2020\]](#page-10-10).

The applications of such smaller, distilled datasets are diverse. For nonparametric methods that scale poorly with the training dataset (e.g. nearest-neighbors or kernel-ridge regression), having a reduced dataset decreases the associated memory and inference costs. For the training of neural networks, such distilled datasets have found several applications in the literature, including increasing the

<sup>∗</sup>Work done while at Google Research.

effectiveness of replay methods in continual learning [\[Borsos et al.,](#page-10-11) [2020\]](#page-10-11) and helping to accelerate neural architecture search [\[Zhao et al.,](#page-10-12) [2021,](#page-10-12) [Zhao and Bilen,](#page-10-7) [2021\]](#page-10-7).

In this paper, we perform a large-scale extension of the methods of [Nguyen et al.](#page-10-6) [\[2021\]](#page-10-6) to obtain new state-of-the-art (SOTA) dataset distillation results. Specifically, we apply the algorithms KIP (Kernel Inducing Points) and LS (Label Solve), first developed in [Nguyen et al.](#page-10-6) [\[2021\]](#page-10-6), to infinitely wide convolutional networks by implementing a novel, distributed meta-learning framework that draws upon hundreds of accelerators per training. The need for such resources is necessitated by the computational costs of using infinitely wide neural networks built out of components occurring in modern image classification models: convolutional and pooling layers (see [§B](#page-15-0) for details). The consequence is that we obtain distilled datasets that are effective for both kernel ridge-regression and neural network training.

Additionally, we initiate a preliminary study of the images and labels which KIP learns. We provide a visual and quantitative analysis of the data learned and find some surprising results concerning their interpretability and their dimensional and spectral properties. Given the efficacy of KIP and LS learned data, we believe a better understanding of them would aid in the understanding of feature learning in neural networks.

To summarize, our contributions are as follows:

- 1. We achieve SOTA dataset distillation results on a wide variety of datasets (MNIST, Fashion-MNIST, SVHN, CIFAR-10, CIFAR-100) for both kernel ridge-regression and neural network training. In several instances, our results achieve an impressively wide margin over prior art, including over 25% and 37% absolute gain in accuracy on CIFAR-10 and SVHN image classification, respectively, when using only 10 images (Tables [1,](#page-3-0) [2,](#page-5-0) [A11\)](#page-24-0).
- 2. We develop a novel, distributed meta-learning framework specifically tailored to the computational burdens of sophisticated neural kernels ([§2.1\)](#page-2-0).
- 3. We highlight and analyze some of the peculiar features of the distilled datasets we obtain, illustrating how they differ from natural data ([§4\)](#page-5-1).
- 4. We open source the distilled datasets, which used thousands of GPU hours, for the research community to further investigate at [https://github.com/google-research/](https://github.com/google-research/google-research/tree/master/kip) [google-research/tree/master/kip](https://github.com/google-research/google-research/tree/master/kip).

## 2 Setup

Background on infinitely wide convolutional networks. Recent literature has established that Bayesian and gradient-descent trained neural networks converge to Gaussian Processes (GP) as the number of hidden units in intermediary layers approaches infinity (see [§5\)](#page-8-0). These results hold for many different architectures, including convolutional networks, which converge to a particular GP in the limit of infinite channels [\[Novak et al.,](#page-10-13) [2019,](#page-10-13) [Garriga-Alonso et al.,](#page-10-14) [2019,](#page-10-14) [Arora et al.,](#page-10-15) [2019\]](#page-10-15). Bayesian networks are described by the Neural Network Gaussian Process (NNGP) kernel, while gradient descent networks are described by the Neural Tangent Kernel (NTK). Since we are interested in synthesizing datasets that can be used with both kernel methods and common gradient-descent trained neural networks, we focus on NTK in this work.

Infinitely wide networks have been shown to achieve SOTA (among non-parametric kernels) results on image classification tasks [\[Novak et al.,](#page-10-13) [2019,](#page-10-13) [Arora et al.,](#page-10-15) [2019,](#page-10-15) [Li et al.,](#page-10-16) [2019,](#page-10-16) [Shankar et al.,](#page-10-17) [2020,](#page-10-17) [Bietti,](#page-10-18) [2021\]](#page-10-18) and even rival finite-width networks in certain settings [\[Arora et al.,](#page-11-0) [2020,](#page-11-0) [Lee](#page-11-1) [et al.,](#page-11-1) [2020\]](#page-11-1). This makes such kernels especially suitable for our task. As convolutional models, they encode useful inductive biases of locality and translation invariance [\[Novak et al.,](#page-10-13) [2019\]](#page-10-13), which enable good generalization. Moreover, flexible and efficient computation of these kernels are possible due to the Neural Tangent library [\[Novak et al.,](#page-11-2) [2020\]](#page-11-2).

Specific models considered. The central neural network (and corresponding infinite-width model) we consider is a simple 4-layer convolutional network with average pooling layers that we refer to as ConvNet throughout the text. This architecture is a slightly modified version of the default model used by [Zhao and Bilen](#page-10-7) [\[2021\]](#page-10-7), [Zhao et al.](#page-10-12) [\[2021\]](#page-10-12) and was chosen for ease of baselining (see [§A](#page-13-0) for details). In several other settings we also consider convolutional networks without pooling

layers ConvVec, [2](#page-2-1) and networks with no convolutions and only fully-connected layers FC. Depth of architecture (as measured by number of hidden layers) is indicated by an integer suffix.

Background on algorithms. We review the Kernel Inducing Points (KIP) and Label Solve (LS) algorithms introduced by [Nguyen et al.](#page-10-6) [\[2021\]](#page-10-6). Given a kernel K, the kernel ridge-regression (KRR) loss function trained on a support dataset (Xs, ys) and evaluated on a target dataset (X<sup>t</sup> , yt) is

<span id="page-2-2"></span>
$$
L(X_s, y_s) = \frac{1}{2} \|y_t - K_{X_t X_s} (K_{X_s X_s} + \lambda I)^{-1} y_s\|_2^2, \tag{1}
$$

where if U and V are sets, KUV is the matrix of kernel elements (K(u, v))u∈U,v∈<sup>V</sup> . Here λ > 0 is a fixed regularization parameter. The KIP algorithm consists of minimizing [\(1\)](#page-2-2) with respect to the support set (either just the X<sup>s</sup> or along with the labels ys). Here, we sample (X<sup>t</sup> , yt) from a target dataset D at every (meta)step, and update the support set using gradient-based methods. Additional variations include augmenting the X<sup>t</sup> or sampling a different kernel K (from a fixed family of kernels) at each step.

The Label Solve algorithm consists of solving for the least-norm minimizer of [\(1\)](#page-2-2) with respect to ys. This yields the labels

$$
y_s^* = \left(K_{X_t X_s} (K_{X_s X_s} + \lambda I)^{-1}\right)^+ y_t,
$$
\n(2)

where A + denotes the pseudo-inverse of the matrix A. Note that here (X<sup>t</sup> , yt) = D, i.e. the labels are solved using the whole target set.

In our applications of KIP and Label Solve, the target dataset D is always significantly larger than the support set (Xs, ys). Hence, the learned support set or solved labels can be regarded as distilled versions of their respective targets. We also initialize our support images to be a subset of natural images, though they could also be initialized randomly.

Based on the infinite-width correspondence outlined above and in [§5,](#page-8-0) dataset distillation using KIP or LS that is optimized for KRR should extend to the corresponding finite-width neural network training. Our experimental results in [§3](#page-3-1) validate this expectation across many settings.

#### <span id="page-2-0"></span>2.1 Client-Server Distributed Workflow

We invoke a client-server model of distributed computation[3](#page-2-3) , in which a server distributes independent workloads to a large pool of client workers that share a queue for receiving and sending work. Our distributed implementation of the KIP algorithm has two distinct stages:

Forward pass: In this step, we compute the support-support and target-support matrices K(Xs, Xs) and K(X<sup>t</sup> , Xs). To do so, we partition X<sup>s</sup> × X<sup>s</sup> and X<sup>t</sup> × X<sup>s</sup> into pairs of images (x, x ′ ), each with batch size B. We send such pairs to workers compute the respective matrix block K(x, x ′ ). The server aggregates all these blocks to obtain the K(Xs, Xs) and K(X<sup>t</sup> , Xs) matrices.

Backward pass: In this step, we need to compute the gradient of the loss L [\(1\)](#page-2-2) with respect to the support set Xs. We need only consider ∂L/∂X<sup>s</sup> since ∂L/∂y<sup>s</sup> is cheap to compute. By the chain rule, we can write

$$
\frac{\partial L}{\partial X_s} = \frac{\partial L}{\partial (K(X_s, X_s))} \frac{\partial K(X_s, X_s)}{\partial X_s} + \frac{\partial L}{\partial (K(X_t, X_s))} \frac{\partial K(X_t, X_s)}{\partial X_s}.
$$

The derivatives of L with respect to the kernel matrices are inexpensive, since L depends in a simple way on them (matrix multiplication and inversion). What is expensive to compute is the derivative of the kernel matrices with respect to the inputs. Each kernel element is an independent function of the inputs and a naive computation of the derivative of a block would require forwardmode differentiation, infeasible due to the size of the input images and the cost to compute the individual kernel elements. Thus our main novelty is to divide up the gradient computation into backward differentiation sub-computations, specifically by using the built-in function jax.vjp in JAX [\[Bradbury et al.,](#page-11-3) [2018\]](#page-11-3). Denoting K = K(Xs, Xs) or K(X<sup>t</sup> , Xs) for short-hand, we divide the

<span id="page-2-1"></span><sup>2</sup> The abbreviation "Vec" stands for vectorization, to indicate that activations of the network are vectorized (flattened) before the top fully-connected layer instead of being pooled.

<span id="page-2-3"></span><sup>3</sup> Implemented using Courier available at <https://github.com/deepmind/launchpad>.

<span id="page-3-0"></span>Table 1: Comparison with other methods. The left group consists of neural network based methods. The right group consists of kernel ridge-regression. All settings for KIP involve the use of label-learning. Grayscale datasets use standard channel-wise preprocessing while RGB datasets use regularized ZCA preprocessing.

|           | Imgs/ | DC1      | DSA1     | KIP FC1  | LS ConvNet2,3 |          | KIP ConvNet2 |
|-----------|-------|----------|----------|----------|---------------|----------|--------------|
|           | Class |          |          | aug      |               | no aug   | aug          |
|           | 1     | 91.7±0.5 | 88.7±0.6 | 85.5±0.1 | 73.4          | 97.3±0.1 | 96.5±0.1     |
| MNIST     | 10    | 97.4±0.2 | 97.8±0.1 | 97.2±0.2 | 96.4          | 99.1±0.1 | 99.1±0.1     |
|           | 50    | 98.8±0.1 | 99.2±0.1 | 98.4±0.1 | 98.3          | 99.4±0.1 | 99.5±0.1     |
|           | 1     | 70.5±0.6 | 70.6±0.6 | -        | 65.3          | 82.9±0.2 | 76.7±0.2     |
| Fashion   | 10    | 82.3±0.4 | 84.6±0.3 | -        | 80.8          | 91.0±0.1 | 88.8±0.1     |
| MNIST     | 50    | 83.6±0.4 | 88.7±0.2 | -        | 86.9          | 92.4±0.1 | 91.0±0.1     |
|           | 1     | 31.2±1.4 | 27.5±1.4 | -        | 23.9          | 62.4±0.2 | 64.3±0.4     |
| SVHN      | 10    | 76.1±0.6 | 79.2±0.5 | -        | 52.8          | 79.3±0.1 | 81.1±0.5     |
|           | 50    | 82.3±0.3 | 84.4±0.4 | -        | 76.8          | 82.0±0.1 | 84.3±0.1     |
| CIFAR-10  | 1     | 28.3±0.5 | 28.8±0.7 | 40.5±0.4 | 26.1          | 64.7±0.2 | 63.4±0.1     |
|           | 10    | 44.9±0.5 | 52.1±0.5 | 53.1±0.5 | 53.6          | 75.6±0.2 | 75.5±0.1     |
|           | 50    | 53.9±0.5 | 60.6±0.5 | 58.6±0.4 | 65.9          | 78.2±0.2 | 80.6±0.1     |
| CIFAR-100 | 1     | 12.8±0.3 | 13.9±0.3 | -        | 23.8          | 34.9±0.1 | 33.3±0.3     |
|           | 10    | 25.2±0.3 | 32.3±0.3 | -        | 39.2          | 47.9±0.2 | 49.5±0.3     |

<sup>1</sup> DC [\[Zhao et al.,](#page-10-12) [2021\]](#page-10-12), DSA [\[Zhao and Bilen,](#page-10-7) [2021\]](#page-10-7), KIP FC [\[Nguyen et al.,](#page-10-6) [2021\]](#page-10-6).

<sup>2</sup> Ours. 3

LD [\[Bohdal et al.,](#page-10-5) [2020\]](#page-10-5) is another baseline which distills only labels using the AlexNet architecture. Our LS achieves higher test accuracy than theirs in every dataset category.

matrix ∂L/∂K, computed on the server, into B × B blocks corresponding to ∂L/∂K(x, x ′ ), where x and x ′ each have batch size B. We send each such block, along with the corresponding block of image data (x, x ′ ), to a worker. The worker then treats the ∂L/∂K(x, x ′ ) it receives as the cotangent vector argument of jax.vjp that, via contraction, converts the derivative of K(x, x ′ ) with respect to x into a scalar. The server aggregates all these partial gradient computations performed by the workers, over all possible B × B blocks, to compute the total gradient ∂L/∂X<sup>s</sup> used to update Xs.

# <span id="page-3-1"></span>3 Experimental Results

#### <span id="page-3-2"></span>3.1 Kernel Distillation Results

We apply the KIP and LS algorithms using the ConvNet architecture on the datasets MNIST [\[LeCun](#page-11-4) [et al.,](#page-11-4) [2010\]](#page-11-4), Fashion MNIST [\[Xiao et al.,](#page-11-5) [2017\]](#page-11-5), SVHN [\[Netzer et al.,](#page-11-6) [2011\]](#page-11-6), CIFAR-10 [\[Krizhevsky,](#page-11-7) [2009\]](#page-11-7), and CIFAR-100. Here, the goal is to condense the train dataset down to a learned dataset of size 1, 10, or 50 images per class. We consider a variety of hyperparameter settings (image preprocessing method, whether to augment target data, and whether to train the support labels for KIP), the full details of which are described in [§A.](#page-13-0) For space reasons, we show a subset of our results in Table [1,](#page-3-0) with results corresponding to the remaining set of hyperparameters left to Tables [A3-](#page-20-0)[A10.](#page-23-0) We highlight here that a crucial ingredient for our strong results in the RGB dataset setting is the use of regularized ZCA preprocessing. Note the variable effect that our augmentations have on performance (see the last two columns of Table [1\)](#page-3-0): they typically only provides a benefit for a sufficiently large support set. This result is consistent with [Zhao et al.](#page-10-12) [\[2021\]](#page-10-12), [Zhao and Bilen](#page-10-7) [\[2021\]](#page-10-7), in which gains from augmentations are also generally obtained from larger support sets. We tried varying the fraction (0.25 and 0.5) of each target batch that is augmented at each training step and found that while 10 images still did best without augmentations, 100 and 500 images typically did slightly better with some partial augmentations (versus none or full). For instance, for 500 images on CIFAR-10, we obtained 81.1% test accuracy using augmentation rate 0.5. Thus, our observation is that given that larger support sets can distill larger target datasets, as the former increases in size, the latter can be augmented more aggressively for obtaining optimal generalization performance.

Image /page/4/Figure/0 description: The image displays a multi-panel figure with a stacked bar chart on the left and four line plots arranged in a 2x2 grid on the right. The stacked bar chart, titled implicitly by its axes, shows 'Test acc' on the y-axis (from 0.0 to 0.6) and 'Test kernel' on the x-axis with categories 'ConvNet', 'Conv-Vec3', and 'Conv-Vec8'. The bars are stacked by 'Train kernels' indicated by a legend: 'all' (light blue), 'ConvNet' (green), 'Conv-Vec8' (orange), and 'Conv-Vec3' (red). For the 'ConvNet' test kernel, the total test accuracy is approximately 0.6, with contributions from ConvNet (blue, ~0.48), Conv-Vec8 (orange, ~0.07), Conv-Vec3 (red, ~0.03), and all (green, ~0.02). For 'Conv-Vec3' test kernel, the total test accuracy is approximately 0.51, with contributions from ConvNet (blue, ~0.36), Conv-Vec8 (orange, ~0.10), Conv-Vec3 (red, ~0.04), and all (green, ~0.01). For 'Conv-Vec8' test kernel, the total test accuracy is approximately 0.50, with contributions from ConvNet (blue, ~0.35), Conv-Vec8 (orange, ~0.10), Conv-Vec3 (red, ~0.04), and all (green, ~0.01). The four line plots all share 'KIP Training Steps' on the x-axis (log scale from 10^0 to 10^4) and 'Test Accuracy' on the y-axis (from 0.2 to 0.45). Each plot shows three lines representing 'ConvNet' (blue), 'Conv-Vec8' (orange), and 'Conv-Vec3' (red). The top-left plot, 'Kernel Sampling Training NN Transfer (MSE)', shows ConvNet accuracy increasing from ~0.22 to ~0.45, Conv-Vec8 from ~0.2 to ~0.39, and Conv-Vec3 from ~0.2 to ~0.38, all peaking around 10^3 steps. The top-right plot, 'Kernel Sampling Training NN Transfer (XENT)', shows ConvNet accuracy increasing from ~0.22 to ~0.44, Conv-Vec8 from ~0.2 to ~0.46, and Conv-Vec3 from ~0.2 to ~0.47, all peaking around 10^3 steps. The bottom-left plot, 'ConvNet Training NN Transfer (MSE)', shows ConvNet accuracy increasing from ~0.22 to ~0.45, while Conv-Vec8 and Conv-Vec3 accuracies increase from ~0.2 to ~0.28 and then plateau. The bottom-right plot, 'ConvNet Training NN Transfer (XENT)', shows ConvNet accuracy increasing from ~0.22 to ~0.41, Conv-Vec8 from ~0.2 to ~0.37, and Conv-Vec3 from ~0.2 to ~0.38, all peaking around 10^3 steps.

<span id="page-4-0"></span>Figure 1: KIP with kernel sampling vs individual kernels. *Left:* Evaluation of three kernels, ConvNet, Conv-Vec3, Conv-Vec8 for KRR with respect to four train settings: sampling KIP ("all") which uses all the kernels or else KIP trained with the individual kernels. For all three kernels, "all" is a close second place, outperformed only if the kernel used for training is exactly the same as the one used for testing. *Right:* We take the learned images of the four train settings described above and transfer them to finite-width neural networks corresponding to ConvNet, Conv-Vec3, Conv-Vec8. Each point is a neural network trained on a specified KIP learned checkpoint. Top row is sampling KIP images and bottom row is the baseline using just ConvNet for KIP. These plots indicate that sampling KIP improves performance across the architectures that are sampled, for both MSE and cross entropy loss. Settings: CIFAR-10, 100 images, no augmentations, no ZCA, no label learning.

Remarkably, our results in Table [1](#page-3-0) outperform all prior baselines across all dataset settings. Our results are especially strong in the small support size regime, with our 1 image per class results for KRR outperforming over 100 times as many natural images (see Table [A1\)](#page-16-0). We also obtain a significant margin over prior art across all datasets, with our largest margin being a 37% absolute gain in test accuracy for the SVHN dataset.

#### 3.2 Kernel Transfer

In [§3.1,](#page-3-2) we focused on obtaining state-of-the-art dataset distillation results for image classification using a specific kernel (ConvNet). Here, we consider the variation of KIP in which we sample from a family of kernels (which we call sampling KIP). We validate that sampling KIP adds robustness to the learned images in that they perform well for the family of kernels sampled during training.

In Figure [1,](#page-4-0) we plot test performance of sampling KIP when using the kernels ConvNet, Conv-Vec3, and Conv-Vec8 (denoted by "all") alongside KIP trained with just the individual kernels. Sampling KIP performs well at test time when using any of the three kernels, whereas datasets trained using a single kernel have a significant performance drop when using a different kernel.

#### 3.3 Neural Network Transfer

In this section, we study how our distilled datasets optimized using KIP and LS transfer to the setting of finite-width neural networks. The main results are shown in Table [2.](#page-5-0) The third column shows the best neural network performance obtained by training on a KIP dataset of the corresponding size with respect to some choice of KIP and neural network training hyperparameters (see [§A](#page-13-0) for details). Since the datasets are optimized for kernel ridge-regression and not for neural network training itself, we expect some performance loss when transferring to finite-width networks, which we record in the fourth column. Remarkably, the drop due to this transfer is quite moderate or small and sometimes the transfer can even lead to gain in performance (see LS for SVHN dataset with 10 images per class).

Overall, our transfer to finite-width networks outperforms prior art based on DC/DSA [\[Zhao et al.,](#page-10-12) [2021,](#page-10-12) [Zhao and Bilen,](#page-10-7) [2021\]](#page-10-7) in the 1 image per class setting for all the RGB datasets (SVHN, CIFAR-10, CIFAR-100). Moreover, for CIFAR-10, we outperform DC/DSA in all settings.

<span id="page-5-0"></span>Table 2: Transfer of KIP and LS to neural network training. Datasets obtained from KIP and LS using the ConvNet kernel are optimized for kernel ridge-regression and thus have reduced performance when used for training the corresponding finite-width ConvNet neural network. Remarkably, the loss in performance is mostly moderate and even small in many instances. Grayscale datasets use standard channel-wise preprocessing while RGB datasets use regularized ZCA preprocessing. The KIP datasets used here can have augmentations or no augmentations and, unlike those in Table 1, can have either fixed or learned labels. ∗ denotes best chosen transfer is obtained with learned labels.

|               | Imgs/Class | DC/DSA                       | KIP to NN                     | Perf.<br>change | LS to NN   | Perf.<br>change |
|---------------|------------|------------------------------|-------------------------------|-----------------|------------|-----------------|
| MNIST         | 1          | <b><math>91.7±0.5</math></b> | $90.1±0.1$                    | -5.5            | $71.0±0.2$ | -2.4            |
|               | 10         | <b><math>97.8±0.1</math></b> | $97.5±0.0$                    | -1.1            | $95.2±0.1$ | -1.2            |
|               | 50         | <b><math>99.2±0.1</math></b> | $98.3±0.1$                    | -0.8            | $97.9±0.0$ | -0.4            |
| Fashion-MNIST | 1          | $70.6±0.6$                   | <b><math>73.5±0.5</math>*</b> | -9.8            | $61.2±0.1$ | -4.1            |
|               | 10         | $84.6±0.3$                   | <b><math>86.8±0.1</math></b>  | -1.3            | $79.7±0.1$ | -1.2            |
|               | 50         | <b><math>88.7±0.2</math></b> | <b><math>88.0±0.1</math>*</b> | -4.5            | $85.0±0.1$ | -1.8            |
| SVHN          | 1          | $31.2±1.4$                   | <b><math>57.3±0.1</math>*</b> | -8.3            | $23.8±0.2$ | -0.2            |
|               | 10         | <b><math>79.2±0.5</math></b> | $75.0±0.1$                    | -1.6            | $53.2±0.3$ | 0.4             |
|               | 50         | <b><math>84.4±0.4</math></b> | $80.5±0.1$                    | -1.0            | $76.5±0.3$ | -0.4            |
| CIFAR-10      | 1          | $28.8±0.7$                   | <b><math>49.9±0.2</math></b>  | -9.2            | $24.7±0.1$ | -1.4            |
|               | 10         | $52.1±0.5$                   | <b><math>62.7±0.3</math></b>  | -4.6            | $49.3±0.1$ | -4.3            |
|               | 50         | $60.6±0.5$                   | <b><math>68.6±0.2</math></b>  | -4.5            | $62.0±0.2$ | -3.9            |
| CIFAR-100     | 1          | $13.9±0.3$                   | <b><math>15.7±0.2</math>*</b> | -18.1           | $11.8±0.2$ | -12.0           |
|               | 10         | <b><math>32.3±0.3</math></b> | $28.3±0.1$                    | -17.4           | $25.0±0.1$ | -14.2           |

Figures [2](#page-6-0) and [3](#page-6-1) provide a closer look at KIP transfer changes under various settings. The first of these tracks how transfer performance changes when adding additional layers as function of the number of KIP training steps used. The normalization layers appear to harm performance for MSE loss, which can be anticipated from their absence in the KIP and LS optimization procedures. However they appear to provide some benefit for cross entropy loss. For Figure [3,](#page-6-1) we observe that as KIP training progresses, the downstream finite-width network's performance also improves in general. A notable exception is observed when learning the labels in KIP, where longer training steps lead to deterioration of information useful to training finite-width neural networks. We also observe that as predicted by infinite-width theory [\[Jacot et al.,](#page-11-8) [2018,](#page-11-8) [Lee et al.,](#page-11-9) [2019\]](#page-11-9), the overall gap between KIP or LS performance and finite-width neural network decreases as the width increases. While our best performing transfer is obtained with width 1024, Figure [3](#page-6-1) (middle) suggest that even with modest width of 64, our transfer can outperform prior art of 60.6% by [Zhao and Bilen](#page-10-7) [\[2021\]](#page-10-7).

Finally, in Figure [4,](#page-6-2) we investigate the performance of KIP images over the course of training of a single run, as compared to natural images, over a range of hyperparameters. We find the outperformance of KIP images above natural images consistent across hyperparameters and checkpoints. This suggests that our KIP images may also be effective for accelerated hyperparameter search, an application of dataset distillation explored in [Zhao et al.](#page-10-12) [\[2021\]](#page-10-12), [Zhao and Bilen](#page-10-7) [\[2021\]](#page-10-7).

Altogether, we find our neural network training results encouraging. First, it validates the applicability of infinite-width methods to the setting of finite width [\[Huang and Yau,](#page-11-10) [2020,](#page-11-10) [Dyer and Gur-Ari,](#page-11-11) [2020,](#page-11-11) [Andreassen and Dyer,](#page-11-12) [2020,](#page-11-12) [Yaida,](#page-11-13) [2020,](#page-11-13) [Lee et al.,](#page-11-1) [2020\]](#page-11-1). Second, we find some of the transfer results quite surprising, including efficacy of label solve and the use of cross-entropy for certain settings (see [§A](#page-13-0) for the full details).

# <span id="page-5-1"></span>4 Understanding KIP Images and Labels

A natural question to consider is what causes KIP to improve generalization performance. Does it simplify support images, removing noise and minor sources of variation while keeping only the core features shared between many target images of a given class? Or does it make them more complex by producing outputs that combine characteristics of many samples in a single resulting collage? While

Image /page/6/Figure/0 description: The image displays two line graphs side-by-side, comparing CNN architectures based on NN Transfer. Both graphs share the same y-axis, labeled "Test Accuracy," ranging from 0.45 to 0.75, and the same x-axis, labeled "KIP Training Steps," on a logarithmic scale from 10^0 to 10^4. A common legend at the bottom indicates five different CNN architectures: Base (blue line with circles), Layer-Norm (orange line with circles), Instance-Norm (red line with circles), Batch-Norm (green line with circles), and Myrtle5 (purple line with circles). Additionally, a dashed black line labeled "KIP ∞ Width" is present on both graphs, representing a constant test accuracy of approximately 0.715.

The left graph, titled "Compare CNN Architectures: NN Transfer (MSE)," shows the following approximate test accuracies at KIP Training Steps of 1, 10, 100, 1000, and 10000:
- Base: (1, 0.46), (10, 0.55), (100, 0.67), (1000, 0.66), (10000, 0.66)
- Layer-Norm: (1, 0.52), (10, 0.62), (100, 0.68), (1000, 0.67), (10000, 0.67)
- Instance-Norm: (1, 0.51), (10, 0.59), (100, 0.65), (1000, 0.64), (10000, 0.64)
- Batch-Norm: (1, 0.50), (10, 0.57), (100, 0.64), (1000, 0.64), (10000, 0.64)
- Myrtle5: (1, 0.50), (10, 0.54), (100, 0.59), (1000, 0.60), (10000, 0.60)

The right graph, titled "Compare CNN Architectures: NN Transfer (XENT)," shows the following approximate test accuracies at KIP Training Steps of 1, 10, 100, 1000, and 10000:
- Base: (1, 0.45), (10, 0.50), (100, 0.60), (1000, 0.62), (10000, 0.61)
- Layer-Norm: (1, 0.54), (10, 0.61), (100, 0.67), (1000, 0.67), (10000, 0.66)
- Instance-Norm: (1, 0.51), (10, 0.58), (100, 0.65), (1000, 0.60), (10000, 0.59)
- Batch-Norm: (1, 0.50), (10, 0.57), (100, 0.66), (1000, 0.66), (10000, 0.65)
- Myrtle5: (1, 0.46), (10, 0.48), (100, 0.52), (1000, 0.48), (10000, 0.49)

<span id="page-6-0"></span>Figure 2: Robustness to neural network variations. KIP ConvNet images (trained with fixed labels) are tested on variations of the ConvNet neural network, including those which have various normalization layers (layer, instance, batch). A similar architecture to ConvNet, the Myrtle5 architecture (without normalization layers) [\[Shankar et al.,](#page-10-17) [2020\]](#page-10-17), which differs from the ConvNet architecture by having an additional convolutional layer at the bottom and a global average pooling that replaces the final local average pooling at the top, is also tested. Finally, mean-square error is compared with cross-entropy loss (left versus right). Settings: CIFAR-10, 500 images, ZCA, no label learning.

Image /page/6/Figure/2 description: The image displays three line plots, all titled 'ConvNet CIFAR-10' and showing 'Test Accuracy' on the y-axis, ranging from 0.2 to 0.8. The first plot, 'KIP training to NN Transfer', has 'KIP Training Steps' on the x-axis, ranging from 10^0 to 10^4 on a log scale. It shows six lines: 'KIP (a)' (light blue dashed) increases from approximately 0.45 to 0.7; 'KIP (a+l)' (orange dashed-dot) increases from approximately 0.45 to 0.75; 'MSE (a)' (blue with circles) increases from approximately 0.45 to 0.7; 'XENT (a)' (blue dotted with triangles) increases from approximately 0.4 to 0.65 then decreases; 'MSE (a+l)' (orange with circles) increases from approximately 0.45 to 0.7 then slightly decreases; and 'XENT (a+l)' (orange dotted with triangles) increases from approximately 0.4 to 0.5 then decreases. The second plot, 'KIP Transfer vs Standard', and the third plot, 'Label Solve Transfer vs Standard', both have 'Width (Number of Channels)' on the x-axis, ranging from 8 to 1024 on a log scale. In the second plot, 'KIP ∞ Width' (black dashed line) is at approximately 0.71; 'KIP-NN Transfer' (blue with circles) increases from approximately 0.35 to 0.68; and 'Standard-NN Training' (orange with triangles) increases from approximately 0.25 to 0.45. In the third plot, 'LS ∞ Width' (black dashed line) is at approximately 0.66; 'LS-NN Transfer' (blue with circles) increases from approximately 0.33 to 0.62; and 'Standard-NN Training' (orange with triangles) increases from approximately 0.25 to 0.45.

<span id="page-6-1"></span>Figure 3: Variations for neural network transfer. *Left:* Plot of transfer performance as a function of KIP training steps across various train settings. Here (a) denotes augmentations used during KIP training and (a+l) denotes that additionally the labels were learned. MSE and XENT denote mean-square-error and cross entropy loss for the neural network, where for the case of XENT and (a+l), the labels for the neural network are the argmax of the learned labels. *Middle:* Exploring the effect of width on transferability of vanilla KIP data. *Right:* The effect of width on the transferability of label solved data. Settings: CIFAR-10, 500 images, ZCA.

Image /page/6/Figure/4 description: The image displays a grid of eight scatter plots, arranged in two rows and four columns. Each subplot shows 'Natural Test Accuracy' on the x-axis and 'KIP Test Accuracy' on the y-axis, with both axes ranging from 0.3 to 0.7. A diagonal black line from (0.3, 0.3) to (0.7, 0.7) is present in each plot. Data points are colored blue for 'MSE' and orange for 'XENT'. Each subplot is titled 'CIFAR-10 support size: 500@ckpt [number]' and includes a legend with specific KIP and Natural accuracy values for MSE and XENT. The titles and legend values for each subplot are as follows: Top row, from left to right: 1. 'CIFAR-10 support size: 500@ckpt 4' with MSE (KIP: 55.9 Natural: 45.8) and XENT (KIP: 46.3 Natural: 43.3). 2. 'CIFAR-10 support size: 500@ckpt 12' with MSE (KIP: 61.2 Natural: 45.8) and XENT (KIP: 51.8 Natural: 43.3). 3. 'CIFAR-10 support size: 500@ckpt 37' with MSE (KIP: 64.0 Natural: 45.8) and XENT (KIP: 55.3 Natural: 43.3). 4. 'CIFAR-10 support size: 500@ckpt 112' with MSE (KIP: 66.6 Natural: 45.8) and XENT (KIP: 60.9 Natural: 43.3). Bottom row, from left to right: 5. 'CIFAR-10 support size: 500@ckpt 335' with MSE (KIP: 68.0 Natural: 45.8) and XENT (KIP: 64.1 Natural: 43.3). 6. 'CIFAR-10 support size: 500@ckpt 1000' with MSE (KIP: 68.3 Natural: 45.8) and XENT (KIP: 63.3 Natural: 43.3). 7. 'CIFAR-10 support size: 500@ckpt 3000' with MSE (KIP: 67.9 Natural: 45.8) and XENT (KIP: 62.7 Natural: 43.3). 8. 'CIFAR-10 support size: 500@ckpt 10000' with MSE (KIP: 68.0 Natural: 45.8) and XENT (KIP: 61.8 Natural: 43.3).

<span id="page-6-2"></span>Figure 4: Hyperparameter robustness. In the above, KIP images across eight different checkpoints are used to train the ConvNet neural network. Each point in each plot is a neural network training with a different hyperparameter, and its location records the final test accuracy when training on natural images versus the KIP images obtained from initializing from such images. For both MSE and cross entropy loss, KIP images consistently exceed natural images across many hyperparameters. Settings: CIFAR-10, 500 images, ZCA, no augmentations, no label learning.

#### **CIFAR-100**

<span id="page-7-0"></span>Figure 5: Examples of learned images. Images are initialized from natural images in the top row (Init) and converge to images in the bottom row (Trained). Settings: 100 images distilled, no ZCA, no label training, augmentations.

properly answering this question is subject to precise definitions of simplicity and complexity, we find that KIP tends to increase the complexity of pictures based on the following experiments:

- Visual analysis: qualitatively, KIP images tend to be richer in textures and contours.
- Dimensional analysis: KIP produces images of higher intrinsic dimensionality (i.e. an estimate of the dimensionality of the manifold on which the images lie) than natural images.
- Spectral analysis: unlike natural images, for which the bulk of generalization performance is explained by a small number of top few eigendirections, KIP images leverage the whole spectrum much more evenly.

Combined, these results let us conclude that KIP usually increases complexity, integrating features from many target images into much fewer support images.

Visual Analysis. A visual inspection of our learned data leads to intriguing observations in terms of interpretability. Figure [5](#page-7-0) shows examples of KIP learned images from CIFAR-100. The resulting images are heterogeneous in terms of how they can be interpreted as distilling the data. For instance, the distilled apple image seems to consist of many apples nested within a possibly larger apple, whereas the distilled bottle image starts off as two bottles and before transforming into one, while other classes (like the beaver) are altogether visually indistinct. Investigating and quantifying aspects that make these images generalize so well is a promising avenue for future work. We show examples from other datasets in Figure [A2.](#page-17-0)

In Figure [6,](#page-8-1) we compare MNIST KIP data learned with and without label learning. For the latter case with images and labels optimized jointly, while labels become more informative, encoding richer inter-class information, the images become less interpretable. This behavior consistently leads to superior KRR results, but appears to not be leveraged as efficiently in the neural network transfer setting (Table [2\)](#page-5-0). Experimental details can be found in [§A.](#page-13-0)

Dimensional Analysis. We study the intrinsic dimension of KIP images and find that they tend to grow. Intrinsic dimension (ID) was first defined by [Bennett](#page-11-14) [\[1969\]](#page-11-14) as "the number of free parameters required in a hypothetical signal generator capable of producing a close approximation to each signal in the collection". In our context, it is the dimensionality of the manifold embedded into the image space which contains all the support images. Intuitively, simple datasets have a low ID as they can be described by a small number of coordinates on the low-dimensional data manifold.

ID can be defined and estimated differently based on assumptions on the manifold structure and the probability density function of the images on this manifold (see [\[Camastra and Staiano,](#page-11-15) [2016\]](#page-11-15) for review). We use the "Two-NN" method developed by [\[Facco et al.,](#page-11-16) [2017\]](#page-11-16), which makes relatively few assumptions and allows to estimate ID only from two nearest-neighbor distances for each datapoint.

Figure [7](#page-8-2) shows that the ID is increasing for the learned KIP images as a function of the training step across a variety of configurations (training with or without augmentations/label learning) and datasets. One might expect that a distillation procedure should decrease dimensionality. On the other hand, [Ansuini et al.](#page-11-17) [\[2019\]](#page-11-17) showed that ID increases in the earlier layers of a trained neural network. It remains to be understood if this latter observation has any relationship with our increased ID. Note that ZCA preprocessing, which played an important role for getting the best performance for our RGB datasets, increases dimensionality of the underlying data (see Figure [A4\)](#page-19-0).

Image /page/8/Figure/0 description: The image displays a grid of handwritten digits (0-9) and associated bar charts, organized into three main rows: 'Init', 'Trained images only', and 'Trained labels and images'. Each main row is further divided into 'Images' and 'Labels' sub-rows. The image is structured into 10 columns, one for each digit from 0 to 9. In the 'Init' row, the 'Images' sub-row shows clear, distinct white digits on a black background. Below them, the 'Labels' sub-row displays bar charts where a single bar is high for the corresponding digit, indicating a one-hot encoding. In the 'Trained images only' row, the 'Images' sub-row shows digits that are slightly blurred or less distinct than the 'Init' images, presented on a gray background. The 'Labels' sub-row again shows bar charts with a single high bar for the correct digit. In the 'Trained labels and images' row, the 'Images' sub-row displays highly abstract, distorted, and textured patterns in shades of gray, which are difficult to recognize as digits. The 'Labels' sub-row for this section shows bar charts with varying bar heights, some extending below the x-axis, indicating a more complex or distributed label representation, with the x-axis labeled '0123456789'.

<span id="page-8-1"></span>Figure 6: Dataset distillation with trainable and non-trainable labels. *Top row:* initialization of support images and labels. *Middle row:* trained images if labels remain fixed. *Bottom row:* trained images and labels, jointly optimized. Settings: 100 images distilled, no augmentations.

<span id="page-8-2"></span>Figure 7: Intrinsic dimension of a learned datasets grows during training. As training progresses, intrinsic dimension of the learned dataset grows, indicating that training non-trivially transforms the data manifold. See Figures [A2](#page-17-0) and [6](#page-8-1) for visual examples of learned images, and Figures [A3](#page-18-0) and [A4](#page-19-0) for similar observations using other metrics and settings. Settings: 500 images distilled, no ZCA.

Spectral Analysis. Another distinguishing property of KIP images is how their spectral components contribute to performance. In Figure [8,](#page-9-0) we spot how different spectral bands of KIP learned images affect test performance as compared to their initial natural images. Here, we use the FC2, Conv-Vec8, and ConvNet architectures. We note that for natural images (light bars), most of their performance is captured by the top 20% of eigenvalues. For KIP images, the performance is either more evenly distributed across the bands (FC and Conv-Vec8) or else is skewed towards the tail (ConvNet).

## <span id="page-8-0"></span>5 Related Work

Dataset distillation was first studied in [Wang et al.](#page-10-4) [\[2018\]](#page-10-4). The work of [Sucholutsky and Schonlau](#page-11-18) [\[2019\]](#page-11-18), [Bohdal et al.](#page-10-5) [\[2020\]](#page-10-5) build upon it by distilling labels. [Zhao et al.](#page-10-12) [\[2021\]](#page-10-12) proposes condensing a training set by harnessing a gradient matching condition. [Zhao and Bilen](#page-10-7) [\[2021\]](#page-10-7) takes this idea further by applying a suitable augmentation strategy. Note that while [Zhao and Bilen](#page-10-7) [\[2021\]](#page-10-7) is limited in augmentation expressiveness (they have to apply a single augmentation per training iteration), we can sample augmentations independently per image in our target set per train step. Our work together with [Nguyen et al.](#page-10-6) [\[2021\]](#page-10-6) are, to the best of our knowledge, the only works using kernel-based methods for dataset distillation on image classification datasets.

<span id="page-9-0"></span>Figure 8: Spectral contribution to test accuracy shifts to the tail. By setting the ridge-parameter to zero in kernel-ridge regression and composing K −1 <sup>X</sup>sX<sup>s</sup> with the spectral projection onto various eigenspaces, we can explore how different spectral bands affect test accuracy of kernel ridgeregression. We plot the relative change in test accuracy using contiguous bands of 20% of the eigenvalues. Settings: CIFAR-10, 500 images. Further details in [§A.](#page-13-0)

Our use of kernels stems from the correspondence between infinitely-wide neural networks and kernel methods [\[Neal,](#page-11-19) [1994,](#page-11-19) [Lee et al.,](#page-11-20) [2018,](#page-11-20) [Matthews et al.,](#page-11-21) [2018,](#page-11-21) [Jacot et al.,](#page-11-8) [2018,](#page-11-8) [Novak et al.,](#page-10-13) [2019,](#page-10-13) [Garriga-Alonso et al.,](#page-10-14) [2019,](#page-10-14) [Arora et al.,](#page-10-15) [2019,](#page-10-15) [Yang,](#page-12-0) [2019a](#page-12-0)[,b,](#page-12-1) [Hron et al.,](#page-12-2) [2020\]](#page-12-2) and the extended correspondence with the finite width corrections [\[Dyer and Gur-Ari,](#page-11-11) [2020,](#page-11-11) [Huang and Yau,](#page-11-10) [2020,](#page-11-10) [Yaida,](#page-11-13) [2020\]](#page-11-13). These correspondences underlie the transferability of our KRR results to neural networks , and have been utilized in understanding trainability [\[Xiao et al.,](#page-12-3) [2020\]](#page-12-3), generalizations [\[Adlam and](#page-12-4) [Pennington,](#page-12-4) [2020\]](#page-12-4), training dynamics [\[Lewkowycz et al.,](#page-12-5) [2020,](#page-12-5) [Lewkowycz and Gur-Ari,](#page-12-6) [2020\]](#page-12-6), uncertainty [\[Adlam et al.,](#page-12-7) [2021\]](#page-12-7), and demonstrated their effectiveness for smaller datasets [\[Arora](#page-11-0) [et al.,](#page-11-0) [2020\]](#page-11-0) and neural architecture search [\[Park et al.,](#page-12-8) [2020,](#page-12-8) [Chen et al.,](#page-12-9) [2021\]](#page-12-9).

# 6 Conclusion

We performed an extensive study of dataset distillation using the KIP and LS algorithms applied to convolutional architectures, obtaining SOTA results on a variety of image classification datasets. In some cases, our learned datasets were more effective than a natural dataset two orders of magnitude larger in size. There are many interesting followup directions and questions from our work:

First, integrating efficient kernel-approximation methods into our algorithms, such as those of [Zandieh](#page-12-10) [et al.](#page-12-10) [\[2021\]](#page-12-10), will reduce computational burden and enable scaling up to larger datasets. In this direction, the understanding of how various resources (e.g. data, parameter count, compute) scale when optimizing for neural network performance has received significant attention as machine learning models continue to stretch computational limits [\[Hestness et al.,](#page-12-11) [2017,](#page-12-11) [Rosenfeld et al.,](#page-12-12) [2020,](#page-12-12) [Kaplan et al.,](#page-12-13) [2020,](#page-12-13) [Bahri et al.,](#page-12-14) [2021\]](#page-12-14). Developing our understanding of how to harness smaller, yet more useful representations data would aid in such endeavors. In particular, it would be especially interesting to explore how well datasets can be compressed as they scale up in size.

Second, LS and KIP with label learning shows that optimizing labels is a very powerful tool for dataset distillation. The labels we obtain are quite far away from standard, interpretable labels and we feel their effectiveness suggests that understanding of how to optimally label data warrants further study.

Finally, the novel features obtained by our learned datasets, and those of dataset distillation methods in general, may reveal insights into interpretability and the nature of sample-efficient representations. For instance, observe the bee and bicycle images in Figure [5:](#page-7-0) the bee class distills into what appears to be spurious visual features (e.g. pollen), while the bicycle class distills to the essential contours of a typical bicycle. Additional analyses and explorations of this type could offer insights into the perennial question of how neural networks learn and generalize.

Acknowledgments We would like to acknowledge special thanks to Samuel S. Schoenholz, who proposed and helped develop the overall strategy for our distributed KIP learning methodology. We are also grateful to Ekin Dogus Cubuk and Manuel Kroiss for helpful discussions.

## References

- <span id="page-10-0"></span>Alex Krizhevsky, Ilya Sutskever, and Geoffrey E Hinton. Imagenet classification with deep convolutional neural networks. *Advances in neural information processing systems*, 25:1097–1105, 2012.
- <span id="page-10-1"></span>Jacob Devlin, Ming-Wei Chang, Kenton Lee, and Kristina Toutanova. Bert: Pre-training of deep bidirectional transformers for language understanding. *arXiv preprint arXiv:1810.04805*, 2018.
- <span id="page-10-2"></span>Tom B Brown, Benjamin Mann, Nick Ryder, Melanie Subbiah, Jared Kaplan, Prafulla Dhariwal, Arvind Neelakantan, Pranav Shyam, Girish Sastry, Amanda Askell, et al. Language models are few-shot learners. *arXiv preprint arXiv:2005.14165*, 2020.
- <span id="page-10-3"></span>Alexey Dosovitskiy, Lucas Beyer, Alexander Kolesnikov, Dirk Weissenborn, Xiaohua Zhai, Thomas Unterthiner, Mostafa Dehghani, Matthias Minderer, Georg Heigold, Sylvain Gelly, et al. An image is worth 16x16 words: Transformers for image recognition at scale. *arXiv preprint arXiv:2010.11929*, 2020.
- <span id="page-10-4"></span>Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-10-5"></span>Ondrej Bohdal, Yongxin Yang, and Timothy Hospedales. Flexible dataset distillation: Learn labels instead of images. *arXiv preprint arXiv:2006.08572*, 2020.
- <span id="page-10-6"></span>Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridge-regression. In *International Conference on Learning Representations*, 2021.
- <span id="page-10-7"></span>Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, 2021.
- <span id="page-10-8"></span>Andrew Ilyas, Shibani Santurkar, Dimitris Tsipras, Logan Engstrom, Brandon Tran, and Aleksander Madry. Adversarial examples are not bugs, they are features, 2019.
- <span id="page-10-9"></span>Minyoung Huh, Pulkit Agrawal, and Alexei A Efros. What makes imagenet good for transfer learning? *arXiv preprint arXiv:1608.08614*, 2016.
- <span id="page-10-10"></span>Katherine L Hermann and Andrew K Lampinen. What shapes feature representations? exploring datasets, architectures, and training. *arXiv preprint arXiv:2006.12433*, 2020.
- <span id="page-10-11"></span>Zalán Borsos, Mojmír Mutny, and Andreas Krause. Coresets via bilevel optimization for continual learning and ` streaming. *arXiv preprint arXiv:2006.03875*, 2020.
- <span id="page-10-12"></span>Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In *International Conference on Learning Representations*, 2021.
- <span id="page-10-13"></span>Roman Novak, Lechao Xiao, Jaehoon Lee, Yasaman Bahri, Greg Yang, Jiri Hron, Daniel A. Abolafia, Jeffrey Pennington, and Jascha Sohl-Dickstein. Bayesian deep convolutional networks with many channels are gaussian processes. In *International Conference on Learning Representations*, 2019.
- <span id="page-10-14"></span>Adrià Garriga-Alonso, Laurence Aitchison, and Carl Edward Rasmussen. Deep convolutional networks as shallow gaussian processes. In *International Conference on Learning Representations*, 2019.
- <span id="page-10-15"></span>Sanjeev Arora, Simon S Du, Wei Hu, Zhiyuan Li, Russ R Salakhutdinov, and Ruosong Wang. On exact computation with an infinitely wide neural net. In *Advances in Neural Information Processing Systems*, pages 8141–8150. Curran Associates, Inc., 2019.
- <span id="page-10-16"></span>Zhiyuan Li, Ruosong Wang, Dingli Yu, Simon S Du, Wei Hu, Ruslan Salakhutdinov, and Sanjeev Arora. Enhanced convolutional neural tangent kernels. *arXiv preprint arXiv:1911.00809*, 2019.
- <span id="page-10-17"></span>Vaishaal Shankar, Alex Chengyu Fang, Wenshuo Guo, Sara Fridovich-Keil, Ludwig Schmidt, Jonathan Ragan-Kelley, and Benjamin Recht. Neural kernels without tangents. In *International Conference on Machine Learning*, 2020.
- <span id="page-10-18"></span>Alberto Bietti. Approximation and learning with deep convolutional models: a kernel perspective. *arXiv preprint arXiv:2102.10032*, 2021.

- <span id="page-11-0"></span>Sanjeev Arora, Simon S. Du, Zhiyuan Li, Ruslan Salakhutdinov, Ruosong Wang, and Dingli Yu. Harnessing the power of infinitely wide deep nets on small-data tasks. In *International Conference on Learning Representations*, 2020. URL <https://openreview.net/forum?id=rkl8sJBYvH>.
- <span id="page-11-1"></span>Jaehoon Lee, Samuel S Schoenholz, Jeffrey Pennington, Ben Adlam, Lechao Xiao, Roman Novak, and Jascha Sohl-Dickstein. Finite versus infinite neural networks: an empirical study. In *Advances in Neural Information Processing Systems*, 2020.
- <span id="page-11-2"></span>Roman Novak, Lechao Xiao, Jiri Hron, Jaehoon Lee, Alexander A. Alemi, Jascha Sohl-Dickstein, and Samuel S. Schoenholz. Neural tangents: Fast and easy infinite neural networks in python. In *International Conference on Learning Representations*, 2020. URL <https://github.com/google/neural-tangents>.
- <span id="page-11-3"></span>James Bradbury, Roy Frostig, Peter Hawkins, Matthew James Johnson, Chris Leary, Dougal Maclaurin, and Skye Wanderman-Milne. JAX: composable transformations of Python+NumPy programs, 2018. URL <http://github.com/google/jax>.
- <span id="page-11-4"></span>Yann LeCun, Corinna Cortes, and CJ Burges. Mnist handwritten digit database. *ATT Labs [Online]. Available: http://yann.lecun.com/exdb/mnist*, 2, 2010.
- <span id="page-11-5"></span>Han Xiao, Kashif Rasul, and Roland Vollgraf. Fashion-mnist: a novel image dataset for benchmarking machine learning algorithms, 2017.
- <span id="page-11-6"></span>Yuval Netzer, Tao Wang, Adam Coates, Alessandro Bissacco, Bo Wu, and Andrew Y Ng. Reading digits in natural images with unsupervised feature learning. 2011.
- <span id="page-11-7"></span>Alex Krizhevsky. Learning multiple layers of features from tiny images. Technical report, 2009.
- <span id="page-11-8"></span>Arthur Jacot, Franck Gabriel, and Clement Hongler. Neural tangent kernel: Convergence and generalization in neural networks. In *Advances in Neural Information Processing Systems*, 2018.
- <span id="page-11-9"></span>Jaehoon Lee, Lechao Xiao, Samuel S. Schoenholz, Yasaman Bahri, Roman Novak, Jascha Sohl-Dickstein, and Jeffrey Pennington. Wide neural networks of any depth evolve as linear models under gradient descent. In *Advances in Neural Information Processing Systems*, 2019.
- <span id="page-11-10"></span>Jiaoyang Huang and Horng-Tzer Yau. Dynamics of deep neural networks and neural tangent hierarchy. In *International Conference on Machine Learning*, 2020.
- <span id="page-11-11"></span>Ethan Dyer and Guy Gur-Ari. Asymptotics of wide networks from feynman diagrams. In *International Conference on Learning Representations*, 2020. URL <https://openreview.net/forum?id=S1gFvANKDS>.
- <span id="page-11-12"></span>Anders Andreassen and Ethan Dyer. Asymptotics of wide convolutional neural networks. *arxiv preprint arXiv:2008.08675*, 2020.
- <span id="page-11-13"></span>Sho Yaida. Non-Gaussian processes and neural networks at finite widths. In *Mathematical and Scientific Machine Learning Conference*, 2020.
- <span id="page-11-14"></span>R. Bennett. The intrinsic dimensionality of signal collections. *IEEE Transactions on Information Theory*, 15(5): 517–525, 1969. doi: 10.1109/TIT.1969.1054365.
- <span id="page-11-15"></span>Francesco Camastra and Antonino Staiano. Intrinsic dimension estimation: Advances and open problems. *Information Sciences*, 328:26–41, 2016. ISSN 0020-0255. doi: https://doi.org/10.1016/j.ins.2015.08.029. URL <https://www.sciencedirect.com/science/article/pii/S0020025515006179>.
- <span id="page-11-16"></span>Elena Facco, Maria d'Errico, Alex Rodriguez, and Alessandro Laio. Estimating the intrinsic dimension of datasets by a minimal neighborhood information. *Scientific reports*, 7(1):1–8, 2017.
- <span id="page-11-17"></span>A. Ansuini, A. Laio, J. Macke, and D. Zoccolan. Intrinsic dimension of data representations in deep neural networks. In *NeurIPS*, 2019.
- <span id="page-11-18"></span>Ilia Sucholutsky and Matthias Schonlau. Soft-label dataset distillation and text dataset distillation. *arXiv preprint arXiv:1910.02551*, 2019.
- <span id="page-11-19"></span>Radford M. Neal. Priors for infinite networks (tech. rep. no. crg-tr-94-1). *University of Toronto*, 1994.
- <span id="page-11-20"></span>Jaehoon Lee, Yasaman Bahri, Roman Novak, Sam Schoenholz, Jeffrey Pennington, and Jascha Sohl-dickstein. Deep neural networks as gaussian processes. In *International Conference on Learning Representations*, 2018.
- <span id="page-11-21"></span>Alexander G. de G. Matthews, Jiri Hron, Mark Rowland, Richard E. Turner, and Zoubin Ghahramani. Gaussian process behaviour in wide deep neural networks. In *International Conference on Learning Representations*, 2018.

- <span id="page-12-0"></span>Greg Yang. Scaling limits of wide neural networks with weight sharing: Gaussian process behavior, gradient independence, and neural tangent kernel derivation. *arXiv preprint arXiv:1902.04760*, 2019a.
- <span id="page-12-1"></span>Greg Yang. Wide feedforward or recurrent neural networks of any architecture are gaussian processes. In *Advances in Neural Information Processing Systems*, 2019b.
- <span id="page-12-2"></span>Jiri Hron, Yasaman Bahri, Jascha Sohl-Dickstein, and Roman Novak. Infinite attention: NNGP and NTK for deep attention networks. In *International Conference on Machine Learning*, 2020.
- <span id="page-12-3"></span>Lechao Xiao, Jeffrey Pennington, and Samuel S Schoenholz. Disentangling trainability and generalization in deep learning. In *International Conference on Machine Learning*, 2020.
- <span id="page-12-4"></span>Ben Adlam and Jeffrey Pennington. The neural tangent kernel in high dimensions: Triple descent and a multi-scale theory of generalization. In *International Conference on Machine Learning*, pages 74–84. PMLR, 2020.
- <span id="page-12-5"></span>Aitor Lewkowycz, Yasaman Bahri, Ethan Dyer, Jascha Sohl-Dickstein, and Guy Gur-Ari. The large learning rate phase of deep learning: the catapult mechanism. *arXiv preprint arXiv:2003.02218*, 2020.
- <span id="page-12-6"></span>Aitor Lewkowycz and Guy Gur-Ari. On the training dynamics of deep networks with l\_2 regularization. In *Advances in Neural Information Processing Systems*, volume 33, pages 4790–4799, 2020.
- <span id="page-12-7"></span>Ben Adlam, Jaehoon Lee, Lechao Xiao, Jeffrey Pennington, and Jasper Snoek. Exploring the uncertainty properties of neural networks' implicit priors in the infinite-width limit. In *International Conference on Learning Representations*, 2021.
- <span id="page-12-8"></span>Daniel S Park, Jaehoon Lee, Daiyi Peng, Yuan Cao, and Jascha Sohl-Dickstein. Towards nngp-guided neural architecture search. *arXiv preprint arXiv:2011.06006*, 2020.
- <span id="page-12-9"></span>Wuyang Chen, Xinyu Gong, and Zhangyang Wang. Neural architecture search on imagenet in four {gpu} hours: A theoretically inspired perspective. In *International Conference on Learning Representations*, 2021.
- <span id="page-12-10"></span>Amir Zandieh, Insu Han, Haim Avron, Neta Shoham, Chaewon Kim, and Jinwoo Shin. Scaling neural tangent kernels via sketching and random features. In *Advances in Neural Information Processing Systems*, 2021.
- <span id="page-12-11"></span>Joel Hestness, Sharan Narang, Newsha Ardalani, Gregory F. Diamos, Heewoo Jun, Hassan Kianinejad, Md. Mostofa Ali Patwary, Yang Yang, and Yanqi Zhou. Deep learning scaling is predictable, empirically. *CoRR*, abs/1712.00409, 2017. URL <http://arxiv.org/abs/1712.00409>.
- <span id="page-12-12"></span>Jonathan S. Rosenfeld, Amir Rosenfeld, Yonatan Belinkov, and Nir Shavit. A constructive prediction of the generalization error across scales. In *International Conference on Learning Representations*, 2020.
- <span id="page-12-13"></span>Jared Kaplan, Sam McCandlish, Tom Henighan, Tom B. Brown, Benjamin Chess, Rewon Child, Scott Gray, Alec Radford, Jeffrey Wu, and Dario Amodei. Scaling laws for neural language models, 2020.
- <span id="page-12-14"></span>Yasaman Bahri, Ethan Dyer, Jared Kaplan, Jaehoon Lee, and Utkarsh Sharma. Explaining neural scaling laws. *arXiv preprint arXiv:2102.06701*, 2021.
- <span id="page-12-15"></span>Ekin D. Cubuk, Barret Zoph, Dandelion Mane, Vijay Vasudevan, and Quoc V. Le. Autoaugment: Learning augmentation strategies from data. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, June 2019.
- <span id="page-12-16"></span>Jascha Sohl-Dickstein, Roman Novak, Samuel S Schoenholz, and Jaehoon Lee. On the infinite width limit of neural networks with a standard parameterization. *arXiv preprint arXiv:2001.07301*, 2020.

# <span id="page-13-0"></span>A Experimental Details

We provide the details of the various configurations in our experiments:

- Augmentations: For RGB datasets, we apply learned policy found in the AutoAugment [\[Cubuk](#page-12-15) [et al.,](#page-12-15) [2019\]](#page-12-15) scheme, followed by horizontal flips. We found crops and cutout to harm performance in our experiments. For grayscale datasets, we implemented augmentations in Keras's tf.keras.preprocessing.image.ImageDataGenerator class, using rotations up to 10 degrees and height and width shift range of 0.1.
- Datasets: We use the MNIST, Fashion-MNIST, CIFAR-10, CIFAR-100, and SVHN (cropped) datasets as provided by the tensorflow\_datasets library. When using mean-square error loss, labels are (mean-centered) one-hot labels.
- Initialization: We initialize KIP and LS with class-balanced subsets of the train data (with the corresponding preprocessing). One could also initialize with uniform random noise. We found the final performance and visual quality of the learned images to be similar in either case.
- Preprocessing: We consider two preprocessing schemes. By default, there is *standard preprocessing*, in which channel-wise mean and standard deviation are computed across the train dataset and then used to normalize the train and test data. We also have *(regularized) ZCA*[4](#page-13-1) , which depends on a regularization parameter λ ≥ 0 as follows. First, we flatten the features for each train image and then standardize each feature across the train dataset. The feature-feature covariance matix C has a singular value decomposition C = UΣU T , from which we get a regularized whitening matrix given by W<sup>λ</sup> = Uφλ(Σ)U T , where φ<sup>λ</sup> maps each singular-value µ to (µ + λtrC) −1/2 and where

$$
\overline{\text{tr}}(C) = \text{tr}(C)/\text{len}(C). \tag{3}
$$

Then regularized ZCA is the transformation which applies layer normalization to each flattened feature vector (i.e. standardize each feature vector along the feature axis) and then multiplies the resulting feature by Wλ. (The case λ = 0 is ordinary ZCA preprocessing). Our implementation of regularized ZCA preprocessing follows that of [Shankar et al.](#page-10-17) [\[2020\]](#page-10-17). We apply regularized ZCA preprocessing for RGB datasets, with λ = 100 for SVHN and 0.1 for CIFAR-10 and CIFAR-100 unless stated otherwise. We obtained these values by tuning the regularization strength on a 5K subset of training and validation data. If a dataset has no ZCA preprocessing, then it has standard preprocessing.

• Label training: This is a boolean hyperparameter in all our KIP training experiments. When true, we optimize the support labels y<sup>s</sup> in the KIP algorithm. When false, they remain fixed at their initial (centered one-hot) values.

Other experimental details include the following. We use NTK parameterization[5](#page-13-2) for exact computation of infinite-width neural tangent kernels. For KIP training, we use target batch size of 5K (the number of elements of (X<sup>t</sup> , yt) in [\(1\)](#page-2-2)) and train for up to 50K iterations[6](#page-13-3) (some runs we end early due to training budget or because test performance saturated). We used the Adam optimizer with learning rate 0.04 for CIFAR-10 and CIFAR-100, and 0.01 for the remaining datasets unless otherwise stated. Our kernel regularizer λ in [\(1\)](#page-2-2), instead of being a fixed constant, is adapted to the scale of K<sup>X</sup>sX<sup>s</sup> , namely

$$
\lambda = \lambda_0 \overline{\text{tr}}(K_{X_s X_s})
$$

where <sup>λ</sup><sup>0</sup> <sup>=</sup> <sup>10</sup><sup>−</sup><sup>6</sup> .

ConvNet Architecture. In [Zhao et al.](#page-10-12) [\[2021\]](#page-10-12), [Zhao and Bilen](#page-10-7) [\[2021\]](#page-10-7), the ConvNet architecture used consists of three blocks of convolution, instance normalization, ReLu, and 2x2 average pooling, followed by a linear readout layer. In our work, we remove the instance normalization (in order to invoke the infinite-width limit) and we additionally prepend an additional convolution and ReLu layer at the beginning of the network, making our network consist of 4 convolutional layers instead of 3.[7](#page-13-4) The two architectures are comparable in terms of performance, see [§C.4](#page-17-1) and Tables [A12](#page-24-1) and [A12.](#page-24-1)

Table [1](#page-3-0): All KIP runs use label training and for both KIP and LS, regularized ZCA preprocessing is used for RGB datasets. All KIP test accuracies have mean and standard deviation computed with respect to 5 checkpoints based on 5 lowest train loss checkpoints (we checkpoint every 50 train steps, for which a target batch size of

<span id="page-13-1"></span><sup>4</sup>Our approach is consistent with that of [Shankar et al.](#page-10-17) [\[2020\]](#page-10-17).

<span id="page-13-3"></span><span id="page-13-2"></span><sup>5</sup> Improved standard parameterization of NTK is described in [Sohl-Dickstein et al.](#page-12-16) [\[2020\]](#page-12-16).

<sup>6</sup> In practice, most of the convergence is achieved after a thousand steps, with a slow, logarithmic increase in test performance with more iterations in many instances, usually when there is label training.

<span id="page-13-4"></span><sup>7</sup> The latter modification was unintentional, as this additional block also occurs in the Myrtle architecture used in other kernel based works e.g. [Shankar et al.](#page-10-17) [\[2020\]](#page-10-17), [Lee et al.](#page-11-1) [\[2020\]](#page-11-1), [Nguyen et al.](#page-10-6) [\[2021\]](#page-10-6).

5K means roughly every 5 epochs). Given the expensiveness of our runs (each of which requires hundreds of GPUs), it is prohibitively expensive to average runs over random seeds. In practice, we find very small variance between different runs with the same hyperparameters. The LS numbers, likewise, were computed with respect to a random support set from a single random seed. We use hyperparameters based on the previous discussion, except for the case of SVHN without augmentations. We found training to be unstable early on in this case, possibly due to the ZCA regularization of 100, while being optimal for kernel ridge-regression, leads to poor conditioning for the gradient updates in KIP. So for the case of SVHN without augmentations, we fall back to the CIFAR10 hyperparameters of ZCA regularization of 0.1 and learning rate of 0.04.

Details for neural network transfer: For neural network transfer experiments in Tables [2,](#page-5-0) [A2,](#page-18-1) [A5,](#page-21-0) [A7,](#page-22-0) [A9,](#page-23-1) Figures [1](#page-4-0) (right), [2,](#page-6-0) [3,](#page-6-1) and [4,](#page-6-2) we follow training and optimization hyperparameter tuning scheme of [Lee et al.](#page-11-1) [\[2020\]](#page-11-1), [Nguyen et al.](#page-10-6) [\[2021\]](#page-10-6).

We trained the networks with cosine learning rate decay and momentum optimizer with momentum 0.9. Networks were trained using mean square (MSE) or softmax-cross-entropy (XENT) loss. We used full-batch gradient for support size 10 and 100 whereas for larger support sizes (500, 1000) batch-size of 100 was used.

KIP training checkpoint at steps {0, 1, 4, 12, 37, 112, 335, 1000, 3000, 10000, 20000, 50000} were used for evaluating neural network transfer, with the best results being selected from among them. For each checkpoint transfer, initial learning rate and L2 regularization strength were tuned over a small grid search space. Following the procedure used in [Lee et al.](#page-11-1) [\[2020\]](#page-11-1), learning rate is parameterized with learning rate factor c with respect to the critical learning rate η = c ηcritical where ηcritical ≡ 2/(λmin(Θ) + λmax(Θ)) and Θ is the NTK [\[Lee et al.,](#page-11-9) [2019\]](#page-11-9). In practice, we compute the empirical NTK Θˆ (x, x ′ ) <sup>=</sup> <sup>∑</sup><sup>j</sup> ∂<sup>j</sup> f(x)∂<sup>j</sup> f(x ′ ) on min(64, support\_size) random points in the training set to estimate ηcritical [\[Lee et al.,](#page-11-9) [2019\]](#page-11-9) by maximum eigenvalue of Θˆ (x, x). Grid search was done over the range c ∈ {0.5, 1, 2, 4} and similarly for the L2-regularization strength in the range {0, 10<sup>−</sup><sup>7</sup> , 10<sup>−</sup><sup>5</sup> , 10<sup>−</sup><sup>3</sup> , 10<sup>−</sup><sup>1</sup> }. The best hyperparameters and early stopping are based on performance with respect to a separate 5K validation set. From among all these options, the best result is what appears as an entry in the KIP to NN columns of our tables. The Perf. change column of Table [2](#page-5-0) measures the difference in the neural network performance with the kernel ridge-regression performance of the chosen KIP data. The error bars when listed is standard deviation on top-20 measurements based on validation set.

The number of training steps are chosen to be sufficiently large. For most runs 5,000 steps were enough, however for CIFAR-100 with MSE loss we increased the number of steps to 25,000 and for SVHN we used 10,000 steps to ensure the networks can sufficiently fit the training data. In training full (45K/5K/10K split) CIFAR-10 training dataset in Table [A2,](#page-18-1) we trained for 30,000 steps for XENT loss and 300,000 steps for MSE loss. When standard data augmentation (flip and crop) is used we increase the number of training steps for KIP images and full set of natural images to 50,000 and 500,000 respectively.

Except for the width/channel variation studies, 1,024 channel convolutional layers were used. The network was initialized and parameterized by standard parameterization instead of NTK parameterization. Data preprocessing matched that of corresponding KIP training procedure.

When transferring learned labels and using softmax-cross-entropy loss, we used the one-hot labels obtained by taking the maximum argument index of learned labels as true target class.

Figures [7,](#page-8-2) [A3,](#page-18-0) [A4](#page-19-0): Intrinsic dimension is measured using the code provided by [Ansuini et al.](#page-11-17) [\[2019\]](#page-11-17) [8](#page-14-0) with default settings. Linear dimension is measured by the smallest number of PCA components needed to explain 90% of variance in the data. Gradient [linear] dimension is measured by considering the analytic, infinite-width NTK in place of the data covariance matrix, either for the purpose of computing PCA to establish the linear dimension, or for the purpose of producing the pairwise-distance between gradients. Precisely, for a finite-width neural network f<sup>n</sup> of width n with trainable parameters θ, we can use the parallelogram law to obtain

$$
\left\| \frac{\partial f_n(x_1)}{\partial \theta} - \frac{\partial f_n(x_2)}{\partial \theta} \right\|_2^2 = \left\| \frac{\partial f_n(x_1)}{\partial \theta} \right\|_2^2 + \left\| \frac{\partial f_n(x_2)}{\partial \theta} \right\|_2^2 - 2 \left( \frac{\partial f_n(x_1)^T}{\partial \theta} \frac{\partial f_n(x_2)}{\partial \theta} \right) \tag{4}
$$

$$
= \hat{\Theta}_n(x_1, x_1) + \hat{\Theta}_n(x_2, x_2) - 2\hat{\Theta}_n(x_1, x_2) \xrightarrow[n \to \infty]{} (5)
$$

$$
\Theta(x_1, x_1) + \Theta(x_2, x_2) - 2\Theta(x_1, x_2).
$$
 (6)

Therefore, NTK can be used to compute the limiting pairwise distance between gradients, and consequently the intrinsic dimension of the manifold of these gradients.

Figure [8](#page-9-0): We mix different KIP hyperparameters for variety. ConvNet: ZCA, no label learning; Conv-Vec: no ZCA, no label learning, 8 hidden layers; FC: no ZCA, label learning, and 2 hidden layers. All three settings used augmentations and and target batch size of 50K (full gradient descent) instead of 5K.

<span id="page-14-0"></span><sup>8</sup> <https://github.com/ansuini/IntrinsicDimDeep>

Figure [A1](#page-16-1): The eight hyperparameters are given by dataset size (100 or 500), zca (+) or no zca (-), and train labels (+) or no train labels (-). All runs use no augmentations. The datasets were obtained after 1000 steps of training. Their ordering in terms of test accuracy (in ascending order) are given by (100, -, -), (100, -, +), (500, -, -), (100, +, -), (500, -, +), (100, +, +), (500, +, -), (500, +, +).

## <span id="page-15-0"></span>B Computational Costs

Classical kernels (RBF and Laplace) as well as FC kernels are cheap to compute in the sense that they are dot product kernels: a kernel matrix element k(x, x ′ ) between two inputs only depends on x ⋅ x, x ⋅ x ′ , and x ′ ⋅ x ′ . However, kernels formed out of convolutional and pooling layers introduce computational complexity by having to keep track of pixel-pixel correlations. Consequently, the addition of a convolutional layer introduces an O(d) complexity while an additional (global) pooling layer introduces O(d 2 ) complexity, where d is the spatial dimension of the image (i.e. height times width).

As an illustration, when using the neural\_tangents [\[Novak et al.,](#page-11-2) [2020\]](#page-11-2) library, one is able to use roughly the following kernel batch sizes[9](#page-15-1) on a Tesla V100 GPU (16GB RAM):

> FC1 kernel batch size ∼ 10<sup>4</sup> Conv-Vec1 kernel batch size ∼ 10<sup>3</sup> ConvNet kernel batch size ∼ 10<sup>1</sup> .

Thus, for the convolutional kernels considered in this paper, our KIP algorithm requires many parallel resources in order for training to happen at a reasonable speed. For instance, for a support set of size 100 and a target batch size of 5000, the K(Xs, Xs) and K(X<sup>t</sup> , Xs) matrix that needs to be computed during each train step has roughly 5 × 10<sup>5</sup> many elements. A kernel batch size of B means that we can only compute a B × B sub-block of K(X<sup>t</sup> , Xs) at a time. For B = 18 (the maximum batch size for ConvNet on a V100 GPU), this leads to 1.5 × 10<sup>3</sup> computations. With a cost of about 80 milliseconds to compute each block, this amounts to 120 seconds of work to compute the required kernel matrices per train step. Computing the gradients takes about three times longer, and so this amounts to 460 seconds of time per train step.

Our distributed framework as described in [§2.1,](#page-2-0) using hundreds of GPUs working in parallel, is what enables our training to become computationally feasible.

## C Additional Tables and Figures

#### C.1 KIP Image Analysis

Subsampling. The train and test images from our benchmark datasets are sampled from the distribution of natural images. KIP images however are jointly learned and therefore correlated. In Figure [A1](#page-16-1) this is validated by showing that natural images degrade in performance (test accuracy using kernel ridge-regression) more gracefully than KIP images when subsampling.

Visual analysis. We provide analogous images to Figure [5](#page-7-0) for the remaining datasets in Figure [A2.](#page-17-0)

Dimensional Analysis. We provide some additional plots of dimension-based measures of our learned images in Figures [A3,](#page-18-0) [7.](#page-8-2) In addition to the intrinsic dimension, we measure the linear dimension and the gradient based versions of the intrinsic and linear dimension (see Section [A](#page-13-0) for details).

#### C.2 Natural Image Baselines

We subsample natural image subsets of various sizes and evaluate test accuracy using kernel-ridge regression with respect to the ConvNet kernel. This gives a sense of how well KIP images perform relative to natural images. In particular, from Table [1,](#page-3-0) KIP images using only 1 image per class outperform over 100 images per class.

We also consider how ZCA preprocessing, the presence of instance normalization layers (which recovers the ConvNet used in [Zhao et al.](#page-10-12) [\[2021\]](#page-10-12), [Zhao and Bilen](#page-10-7) [\[2021\]](#page-10-7)), augmentations, and choice of loss function affect neural network training of ConvNet. We record the results with respect to CIFAR-10 in Table [A2.](#page-18-1)

<span id="page-15-1"></span><sup>9</sup>A kernel matrix element K(x, x ′ ) is a function of a pair of images x, x ′ ∈ R H×W×C . By vectorizing the computation, we can apply K to batches of images x, x ′ ∈ R B×H×W×C to obtain a B × B matrix. We call B the (kernel) batch size.

<span id="page-16-1"></span>Figure A1: KIP images are highly correlated. Subsamples of KIP images are compared to the corresponding subsample of natural images at initialization. The relative drop in test accuracy among the two cases are plotted along the axes. KIP images show more severe degradation under subsampling. A selection of eight hyperparameters (see [§A\)](#page-13-0) were chosen among KIP trained images. Each hyperparameter is a differently sized circle, with larger circles indicating higher test accuracy when using full support set (i.e. keep 100% train data). Different colors denote different subset sizes, and test accuracies are computed using the average of 100 randomly sampled subsets. Settings: CIFAR-10.

| Imgs/Class | MNIST    | Fashion MNIST | SVHN     | SVHN (ZCA) |
|------------|----------|---------------|----------|------------|
| 1          | 52.0±5.1 | 47.0±5.7      | 10.8±1.1 | 13.1±1.7   |
| 2          | 65.2±3.2 | 56.8±3.2      | 12.6±1.5 | 14.8±1.6   |
| 4          | 79.9±1.9 | 65.0±3.5      | 13.8±1.3 | 17.2±1.5   |
| 8          | 88.2±1.1 | 70.2±1.8      | 16.8±1.4 | 23.0±1.6   |
| 16         | 93.2±0.7 | 74.9±1.3      | 23.4±1.3 | 32.0±2.1   |
| 32         | 95.5±0.3 | 78.4±0.9      | 32.6±1.4 | 45.2±1.2   |
| 64         | 96.9±0.2 | 82.1±0.5      | 44.3±1.0 | 58.1±1.2   |
| 128        | 97.8±0.1 | 84.8±0.3      | 55.1±0.8 | 67.6±0.9   |
| All        | 99.4     | 93.0          | 85.1     | 88.8       |

<span id="page-16-0"></span>Table A1: ConvNet kernel performance on random subsets. Mean and standard deviation computed over twenty random subsets. "All" row denotes entire dataset.

#### C.3 Ablation Studies

We present a complete hyperparameter sweep for KIP and LS in Table [A3](#page-20-0)[-A10](#page-23-0) by including results for standard preprocessing and no label training, which was not on display in Table [1.](#page-3-0) We also provide corresponding neural network transfer results where available. All hyperparameters are as described in [§A.](#page-13-0) Overall, we observe a clear and consistent benefit of using ZCA regularization and label training for KIP.

<span id="page-17-0"></span>Figure A2: Examples of learned images on more datasets. See Figure [5](#page-7-0) for CIFAR-100. Settings: 10 images distilled, no ZCA, no label training, no augmentations.

#### <span id="page-17-1"></span>C.4 DC/DSA ConvNet

As explained in Section [§A,](#page-13-0) our ConvNet is a slightly modified version of the ConvNet appearing in the baselines [Zhao et al.](#page-10-12) [\[2021\]](#page-10-12), [Zhao and Bilen](#page-10-7) [\[2021\]](#page-10-7). We compute kernel-ridge regression results for CIFAR-10 with the initial convolution and ReLu layer of our ConvNet removed in Tables [A11](#page-24-0) and [A12.](#page-24-1) Since the architecture change is relatively minor, the numbers do not differ as much. Indeed, the differences are typically less than 1% (the largest difference was 1.9%), with the shallower ConvNet sometimes outperforming the deeper ConvNet. This suggests that our overall results are robust with respect to this change and hence are a fair comparison with the shallower 3-layer ConvNet of [Zhao et al.](#page-10-12) [\[2021\]](#page-10-12), [Zhao and Bilen](#page-10-7) [\[2021\]](#page-10-7).

<span id="page-18-0"></span>Figure A3: Dimensionality of learned dataset manifolds grows with training. Similarly to Figure [7,](#page-8-2) we show that dimensionality of the learned dataset grows with training as measured by (top) linear dimension, (middle) intrinsic dimension of infinite-width gradients (a.k.a. NTK features), and (bottom) linear dimension of gradients. Settings: 500 images, no ZCA.

<span id="page-18-1"></span>Table A2: Training on full CIFAR-10 train data with ConvNet neural network. Regularized ZCA, presence of instance normalization layers, augmentation settings, and loss function (mean squared error and cross-entropy) are varied.

| ZCA | Normalization | Data Augmentation | Test Accuracy, %                |
|-----|---------------|-------------------|---------------------------------|
|     |               |                   | MSE: 76.9±0.8<br>XENT: 80.4±0.2 |
|     | ✓             |                   | MSE: 78.8±3.7<br>XENT: 84.9±0.6 |
| ✓   |               |                   | MSE: 82.1±0.2<br>XENT: 84.4±0.1 |
| ✓   | ✓             |                   | MSE: 85.4±1.5<br>XENT: 86.3±0.9 |
|     |               | ✓                 | MSE: 86.6±0.2<br>XENT: 88.9±0.2 |

**Dataset (ZCA)**

**Dataset (ZCA)**

**Dataset (ZCA)**

Figure A4: Change in dimensionality of learned ZCA preprocessed dataset manifolds during training. Similarly to Figure [7](#page-8-2) and [A3,](#page-18-0) dimensionality of the learned dataset generally grows with training as measured by (top to bottom) intrinsic dimension, linear dimension, intrinsic dimension of infinite-width gradients (a.k.a. NTK features), and linear dimension of gradients. However, we remark that the trend is less robust in the ZCA setting, notably the dataset at initialization can have a much higher dimension (y-value at step 0) than without ZCA-preprocessing, and can decrease during training (e.g. top-left plot). Settings: 500 images.

0 78 335 1,50012,000 **Training step**

<span id="page-19-0"></span>0 78 335 1,50012,000 **Training step**

<span id="page-20-0"></span>Table A3: KIP Training for MNIST. Complete set of training options for KIP and the corresponding transfer performance to neural network training (NN column). † denotes best chosen transfer is obtained with XENT loss rather than MSE.

| Imgs/Class | Train Labels | Data Augmentation | KIP Accuracy, % | NN Accuracy, % |
|------------|--------------|-------------------|-----------------|----------------|
| 1          | ✓            | ✓                 | 96.5±0.0        | 82.9±0.3       |
|            | ✓            |                   | 97.3±0.0        | 82.9±0.4       |
|            |              | ✓                 | 95.2±0.2        | 87.2±0.2†      |
|            |              |                   | 95.1±0.1        | 90.1±0.1†      |
| 10         | ✓            | ✓                 | 99.1±0.0        | 95.5±0.0       |
|            | ✓            |                   | 99.1±0.0        | 95.8±0.2       |
|            |              | ✓                 | 98.4±0.0        | 96.6±0.0       |
|            |              |                   | 98.4±0.0        | 97.5±0.0       |
| 50         | ✓            | ✓                 | 99.5±0.0        | 98.1±0.1       |
|            | ✓            |                   | 99.4±0.0        | 98.2±0.2       |
|            |              | ✓                 | 99.1±0.0        | 98.1±0.1       |
|            |              |                   | 99.0±0.0        | 98.3±0.1       |

Table A4: KIP Training for Fashion-MNIST. Complete set of training options for KIP and the corresponding transfer performance to neural network training (NN column).

| Imgs/Class | Train Labels | Data Augmentation | KIP Accuracy, % | NN Accuracy, % |
|------------|--------------|-------------------|-----------------|----------------|
| 1          | ✓            | ✓                 | 76.7±0.2        | 67.3±0.7       |
|            | ✓            |                   | 82.9±0.2        | 73.5±0.5       |
|            |              | ✓                 | 76.6±0.1        | 69.8±0.1       |
|            |              |                   | 78.9±0.2        | 72.1±0.3       |
| 10         | ✓            | ✓                 | 88.8±0.1        | 81.9±0.1       |
|            | ✓            |                   | 91.0±0.1        | 85.1±0.2       |
|            |              | ✓                 | 85.3±0.1        | 82.5±0.2       |
|            |              |                   | 87.6±0.1        | 86.8±0.1       |
| 50         | ✓            | ✓                 | 91.0±0.1        | 85.7±0.1       |
|            | ✓            |                   | 92.4±0.1        | 88.0±0.1       |
|            |              | ✓                 | 88.1±0.2        | 84.0±0.1       |
|            |              |                   | 90.0±0.1        | 86.9±0.1       |

| Imgs/Class | ZCA | Train Labels | Data Augmentation | KIP Accuracy, % | NN Accuracy, % |
|------------|-----|--------------|-------------------|-----------------|----------------|
| 1          | ✓   | ✓            | ✓                 | 63.4±0.1        | 48.7±0.3       |
|            | ✓   | ✓            |                   | 64.7±0.2        | 47.9±0.1       |
|            | ✓   |              | ✓                 | 58.1±0.2        | 49.5±0.4       |
|            | ✓   |              |                   | 58.5±0.4        | 49.9±0.2       |
|            |     | ✓            | ✓                 | 55.1±0.5        | 34.8±0.4       |
|            |     | ✓            |                   | 56.4±0.4        | 36.7±0.5       |
|            |     |              | ✓                 | 50.1±0.1        | 35.3±0.5       |
|            |     |              |                   | 50.7±0.1        | 38.6±0.4       |
| 10         | ✓   | ✓            | ✓                 | 75.5±0.1        | 59.4±0.0       |
|            | ✓   | ✓            |                   | 75.6±0.2        | 58.9±0.1       |
|            | ✓   |              | ✓                 | 66.5±0.3        | 62.6±0.2       |
|            | ✓   |              |                   | 67.6±0.3        | 62.7±0.3       |
|            |     | ✓            | ✓                 | 69.3±0.3        | 45.6±0.1       |
|            |     | ✓            |                   | 69.6±0.2        | 47.4±0.1       |
|            |     |              | ✓                 | 60.4±0.2        | 47.7±0.1       |
|            |     |              |                   | 61.0±0.2        | 49.2±0.1       |
| 50         | ✓   | ✓            | ✓                 | 80.6±0.1        | 64.9±0.2       |
|            | ✓   | ✓            |                   | 78.4±0.3        | 66.1±0.1       |
|            | ✓   |              | ✓                 | 71.4±0.1        | 67.7±0.1       |
|            | ✓   |              |                   | 72.5±0.2        | 68.6±0.2       |
|            |     | ✓            | ✓                 | 74.8±0.3        | 55.0±0.1       |
|            |     | ✓            |                   | 72.1±0.2        | 55.8±0.2       |
|            |     |              | ✓                 | 66.8±0.1        | 56.1±0.2       |
|            |     |              |                   | 67.2±0.2        | 56.7±0.3       |

<span id="page-21-0"></span>Table A5: KIP Training for CIFAR-10. Complete set of training options for KIP (whether to use ZCA regularization, label training, or augmentations) and the corresponding transfer performance to neural network training (NN column).

Table A6: Label Solve on CIFAR-10. We consider both ZCA and no ZCA preprocessing.

| Imgs/Class | ZCA | LS Accuracy, % | NN Accuracy, %       |
|------------|-----|----------------|----------------------|
| 1          | ✓   | 26.1<br>26.3   | 24.7±0.1<br>22.9±0.3 |
| 10         | ✓   | 53.6<br>46.3   | 49.3±0.1<br>41.5±0.2 |
| 50         | ✓   | 65.9<br>57.4   | 62.0±0.2<br>49.0±0.2 |

<span id="page-22-0"></span>Table A7: KIP Training for SVHN. Complete set of training options for KIP (whether to use ZCA regularization, label training, or augmentations) and the corresponding transfer performance to neural network training (NN column). † denotes best chosen transfer is obtained with XENT loss rather than MSE.

| Imgs/Class | ZCA | Train Labels | Data Augmentation | KIP Accuracy, % | NN Accuracy, % |
|------------|-----|--------------|-------------------|-----------------|----------------|
| 1          | ✓   | ✓            | ✓                 | 64.3±0.4        | 57.3±0.1       |
|            | ✓   | ✓            |                   | 62.4±0.2        | -              |
|            | ✓   |              | ✓                 | 48.0±1.1        | 44.5±0.2       |
|            | ✓   |              |                   | 48.1±0.7        | -              |
|            |     | ✓            | ✓                 | 54.5±0.7        | 23.4±0.3       |
|            |     | ✓            |                   | 52.6±1.1        | 39.5±0.4       |
|            |     |              | ✓                 | 40.0±0.5        | 19.6±0.0       |
|            |     |              |                   | 40.2±0.5        | 20.3±0.2†      |
| 10         | ✓   | ✓            | ✓                 | 81.1±0.6        | 74.2±0.2       |
|            | ✓   | ✓            |                   | 79.3±0.1        | -              |
|            | ✓   |              | ✓                 | 75.8±0.1        | 75.0±0.1       |
|            | ✓   |              |                   | 64.1±0.3        | -              |
|            |     | ✓            | ✓                 | 80.4±0.3        | 57.3±1.5       |
|            |     | ✓            |                   | 79.3±0.1        | 59.8±0.3       |
|            |     |              | ✓                 | 77.5±0.3        | 59.9±0.3       |
|            |     |              |                   | 76.5±0.3        | 64.2±0.3       |
| 50         | ✓   | ✓            | ✓                 | 84.3±0.2        | 78.4±0.5       |
|            | ✓   | ✓            |                   | 82.0±0.1        | -              |
|            | ✓   |              | ✓                 | 81.3±0.2        | 80.5±0.1       |
|            | ✓   |              |                   | 72.4±0.3        | -              |
|            |     | ✓            | ✓                 | 84.0±0.3        | 71.0±0.4       |
|            |     | ✓            |                   | 82.1±0.1        | 71.2±1.0       |
|            |     |              | ✓                 | 81.7±0.2        | 72.7±0.4†      |
|            |     |              |                   | 80.8±0.2        | 73.2±0.3†      |

Table A8: Label Solve on SVHN. We consider both ZCA and no ZCA preprocessing.

| Imgs/Class | ZCA | LS Accuracy, % | NN Accuracy, % |
|------------|-----|----------------|----------------|
| 1          | ✓   | 23.9           | 23.8±0.2       |
|            |     | 21.1           | 20.0±0.2       |
| 10         | ✓   | 52.8           | 53.2±0.2       |
|            |     | 40.1           | 37.9±0.2       |
| 50         | ✓   | 76.8           | 76.5±0.3       |
|            |     | 69.2           | 66.3±0.1       |

<span id="page-23-1"></span>Table A9: KIP Training for CIFAR-100. Complete set of training options for KIP (whether to use ZCA regularization, label training, or augmentations) and the corresponding transfer performance to neural network training (NN column). † denotes best chosen transfer is obtained with XENT loss rather than MSE.

| Imgs/Class | ZCA | Train Labels | Data Augmentation | KIP Accuracy, % | NN Accuracy, % |
|------------|-----|--------------|-------------------|-----------------|----------------|
| 1          | ✓   | ✓            | ✓                 | 33.3±0.3        | 15.7±0.2       |
|            | ✓   | ✓            |                   | 34.9±0.1        | -              |
|            | ✓   |              | ✓                 | 30.0±0.2        | 10.8±0.2       |
|            | ✓   |              |                   | 31.8±0.3        | -              |
|            |     | ✓            | ✓                 | 26.2±0.1        | 13.4±0.4       |
|            |     | ✓            |                   | 28.5±0.1        | -              |
|            |     |              | ✓                 | 18.8±0.2        | 8.6±0.1†       |
|            |     |              |                   | 22.0±0.3        | -              |
| 10         | ✓   | ✓            | ✓                 | 51.2±0.2        | 24.2±0.1       |
|            | ✓   | ✓            |                   | 47.9±0.2        | -              |
|            | ✓   |              | ✓                 | 45.2±0.2        | 28.3±0.1       |
|            | ✓   |              |                   | 46.0±0.2        | -              |
|            |     | ✓            | ✓                 | 41.4±0.2        | 20.7±0.4       |
|            |     | ✓            |                   | 42.5±0.3        | -              |
|            |     |              | ✓                 | 37.2±0.1        | 18.6±0.1†      |
|            |     |              |                   | 38.4±0.2        | -              |

| Table A10: Label Solve on CIFAR-100. We consider both ZCA and no ZCA preprocessing. |  |  |
|-------------------------------------------------------------------------------------|--|--|
|-------------------------------------------------------------------------------------|--|--|

<span id="page-23-0"></span>

| Imgs/Class | ZCA | LS Accuracy, % | NN Accuracy, % |
|------------|-----|----------------|----------------|
| 1          | ✓   | 23.8           | 11.8 $±$ 0.2   |
|            |     | 18.1           | 11.2 $±$ 0.3   |
| 10         | ✓   | 39.2           | 25.0 $±$ 0.1   |
|            |     | 31.3           | 20.0 $±$ 0.1   |

| Imgs/Class | ZCA | Train Labels | Data Augmentation | KIP Accuracy, % |
|------------|-----|--------------|-------------------|-----------------|
| 1          | ✓   | ✓            | ✓                 | 62.6±0.2        |
|            | ✓   | ✓            |                   | 65.8±0.2        |
|            | ✓   |              | ✓                 | 58.7±0.5        |
|            | ✓   |              |                   | 59.1±0.4        |
|            |     | ✓            | ✓                 | 53.4±0.3        |
|            |     | ✓            |                   | 56.3±0.3        |
|            |     |              | ✓                 | 48.7±0.6        |
|            |     |              |                   | 50.1±0.2        |
| 10         | ✓   | ✓            | ✓                 | 74.5±0.3        |
|            | ✓   | ✓            |                   | 74.4±0.2        |
|            | ✓   |              | ✓                 | 65.9±0.1        |
|            | ✓   |              |                   | 67.0±0.4        |
|            |     | ✓            | ✓                 | 68.3±0.1        |
|            |     | ✓            |                   | 68.7±0.2        |
|            |     |              | ✓                 | 59.6±0.2        |
|            |     |              |                   | 60.8±0.2        |
| 50         | ✓   | ✓            | ✓                 | 79.6±0.2        |
|            | ✓   | ✓            |                   | 76.5±0.1        |
|            | ✓   |              | ✓                 | 70.4±0.1        |
|            | ✓   |              |                   | 71.7±0.2        |
|            |     | ✓            | ✓                 | 73.7±0.1        |
|            |     | ✓            |                   | 71.5±0.3        |
|            |     |              | ✓                 | 65.9±0.2        |
|            |     |              |                   | 66.9±0.2        |

<span id="page-24-0"></span>Table A11: KIP Training for CIFAR-10 on shallower ConvNet. Complete set of training options for KIP (whether to use ZCA regularization, label training, or augmentations). Here, we remove the prepended convolutional and ReLu layer from ConvNet used in other tables.

<span id="page-24-1"></span>Table A12: Label Solve on CIFAR-10 on shallower ConvNet. We consider both ZCA and no ZCA preprocessing. Here, we remove the prepended convolutional and ReLu layer from ConvNet used in other tables.

| Imgs/Class | ZCA | LS Accuracy, % |
|------------|-----|----------------|
| 1          | ✓   | 26.5<br>26.4   |
| 10         | ✓   | 52.9<br>46.4   |
| 50         | ✓   | 65.5<br>56.9   |