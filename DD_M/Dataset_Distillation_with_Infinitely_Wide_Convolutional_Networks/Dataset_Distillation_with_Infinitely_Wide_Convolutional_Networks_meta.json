{"table_of_contents": [{"title": "Dataset Distillation with Infinitely Wide\nConvolutional Networks", "heading_level": null, "page_id": 0, "polygon": [[159.81700134277344, 99.8338623046875], [452.1861877441406, 99.8338623046875], [452.1861877441406, 136.9742431640625], [159.81700134277344, 136.9742431640625]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[283.1396484375, 267.7872314453125], [328.2432861328125, 267.7872314453125], [328.2432861328125, 279.742431640625], [283.1396484375, 279.742431640625]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[108.0, 454.19415283203125], [191.548828125, 454.19415283203125], [191.548828125, 466.3828125], [108.0, 466.3828125]]}, {"title": "2 Setup", "heading_level": null, "page_id": 1, "polygon": [[106.75634765625, 456.77716064453125], [155.16326904296875, 456.77716064453125], [155.16326904296875, 468.73236083984375], [106.75634765625, 468.73236083984375]]}, {"title": "2.1 Client-Server Distributed Workflow", "heading_level": null, "page_id": 2, "polygon": [[107.20458984375, 415.7545166015625], [283.0229187011719, 415.7545166015625], [283.0229187011719, 425.77734375], [107.20458984375, 425.77734375]]}, {"title": "3 Experimental Results", "heading_level": null, "page_id": 3, "polygon": [[106.98046875, 479.91796875], [236.671875, 479.91796875], [236.671875, 492.71038818359375], [106.98046875, 492.71038818359375]]}, {"title": "3.1 Kernel Distillation Results", "heading_level": null, "page_id": 3, "polygon": [[106.3828125, 505.0546875], [243.84375, 505.0546875], [243.84375, 516.276123046875], [106.3828125, 516.276123046875]]}, {"title": "3.2 Kernel Transfer", "heading_level": null, "page_id": 4, "polygon": [[107.1298828125, 449.75390625], [199.7666015625, 449.75390625], [199.7666015625, 460.7821350097656], [107.1298828125, 460.7821350097656]]}, {"title": "3.3 Neural Network Transfer", "heading_level": null, "page_id": 4, "polygon": [[106.98046875, 576.984375], [238.5897216796875, 576.984375], [238.5897216796875, 587.6121215820312], [106.98046875, 587.6121215820312]]}, {"title": "4 Understanding KIP Images and Labels", "heading_level": null, "page_id": 5, "polygon": [[106.90576171875, 654.71484375], [326.619140625, 654.71484375], [326.619140625, 667.08984375], [106.90576171875, 667.08984375]]}, {"title": "CIFAR-100", "heading_level": null, "page_id": 7, "polygon": [[280.57781982421875, 76.8603515625], [325.91241455078125, 76.8603515625], [325.91241455078125, 86.4237060546875], [280.57781982421875, 86.4237060546875]]}, {"title": "5 Related Work", "heading_level": null, "page_id": 8, "polygon": [[107.279296875, 611.2891540527344], [197.09014892578125, 611.2891540527344], [197.09014892578125, 623.2443542480469], [107.279296875, 623.2443542480469]]}, {"title": "6 Conclusion", "heading_level": null, "page_id": 9, "polygon": [[107.578125, 439.3125], [183.48046875, 439.3125], [183.48046875, 451.6053466796875], [107.578125, 451.6053466796875]]}, {"title": "References", "heading_level": null, "page_id": 10, "polygon": [[106.98046875, 122.3931884765625], [164.8037109375, 122.3931884765625], [164.8037109375, 134.348388671875], [106.98046875, 134.348388671875]]}, {"title": "A Experimental Details", "heading_level": null, "page_id": 13, "polygon": [[106.3828125, 72.55810546875], [237.8671875, 72.55810546875], [237.8671875, 84.7423095703125], [106.3828125, 84.7423095703125]]}, {"title": "B Computational Costs", "heading_level": null, "page_id": 15, "polygon": [[106.3828125, 131.158203125], [236.07421875, 131.158203125], [236.07421875, 143.1134033203125], [106.3828125, 143.1134033203125]]}, {"title": "C Additional Tables and Figures", "heading_level": null, "page_id": 15, "polygon": [[107.05517578125, 435.8201904296875], [282.990234375, 435.8201904296875], [282.990234375, 447.775390625], [107.05517578125, 447.775390625]]}, {"title": "C.1 KIP Image Analysis", "heading_level": null, "page_id": 15, "polygon": [[107.4287109375, 460.3825378417969], [219.4892578125, 460.3825378417969], [219.4892578125, 470.3451232910156], [107.4287109375, 470.3451232910156]]}, {"title": "C.2 Natural Image Baselines", "heading_level": null, "page_id": 15, "polygon": [[107.1298828125, 584.7825164794922], [237.7177734375, 584.7825164794922], [237.7177734375, 594.7451171875], [107.1298828125, 594.7451171875]]}, {"title": "C.3 Ablation Studies", "heading_level": null, "page_id": 16, "polygon": [[107.1298828125, 662.6915130615234], [203.9501953125, 662.6915130615234], [203.9501953125, 672.890625], [107.1298828125, 672.890625]]}, {"title": "C.4 DC/DSA ConvNet", "heading_level": null, "page_id": 17, "polygon": [[107.20458984375, 513.17578125], [212.466796875, 513.17578125], [212.466796875, 524.2990417480469], [107.20458984375, 524.2990417480469]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 151], ["Line", 45], ["Text", 9], ["SectionHeader", 3], ["Footnote", 1], ["<PERSON>Footer", 1], ["<PERSON><PERSON><PERSON><PERSON>", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 214], ["Line", 52], ["Text", 7], ["ListItem", 4], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 552], ["Line", 98], ["TextInlineMath", 6], ["Text", 5], ["Reference", 4], ["Equation", 3], ["Footnote", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 459], ["TableCell", 125], ["Line", 63], ["Text", 3], ["Reference", 3], ["TextInlineMath", 2], ["SectionHeader", 2], ["Caption", 1], ["Table", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 211], ["Line", 83], ["Text", 4], ["SectionHeader", 2], ["Figure", 1], ["Caption", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 2692, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 380], ["TableCell", 197], ["Line", 60], ["Text", 4], ["Reference", 2], ["Caption", 1], ["Table", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 0, "llm_tokens_used": 7804, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 428], ["Line", 141], ["Figure", 3], ["Caption", 3], ["FigureGroup", 3], ["Reference", 3], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 0, "llm_tokens_used": 9601, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 146], ["Line", 45], ["Text", 7], ["ListItem", 3], ["Caption", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 189], ["Line", 47], ["Reference", 3], ["Figure", 2], ["Caption", 2], ["Text", 2], ["FigureGroup", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 1338, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 253], ["Line", 64], ["Text", 5], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 148], ["Line", 48], ["ListItem", 19], ["Reference", 19], ["Text", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 159], ["Line", 50], ["ListItem", 22], ["Reference", 22], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 122], ["Line", 39], ["ListItem", 17], ["Reference", 17], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 376], ["Line", 72], ["ListItem", 5], ["Reference", 5], ["Text", 4], ["Footnote", 4], ["TextInlineMath", 3], ["Equation", 2], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 551], ["Line", 96], ["Text", 8], ["Equation", 3], ["TextInlineMath", 2], ["Footnote", 1], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 371], ["Line", 73], ["Text", 8], ["SectionHeader", 4], ["TextInlineMath", 3], ["Reference", 2], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 251], ["TableCell", 155], ["Line", 49], ["Caption", 2], ["Reference", 2], ["Figure", 1], ["Table", 1], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 4797, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 160], ["Line", 27], ["Reference", 2], ["Figure", 1], ["Caption", 1], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 276], ["Line", 86], ["TableCell", 24], ["Caption", 2], ["Reference", 2], ["Figure", 1], ["Table", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 1, "llm_error_count": 1, "llm_tokens_used": 2528, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 221], ["Line", 75], ["Text", 5], ["Figure", 3], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 3, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 242], ["Span", 221], ["Line", 38], ["Caption", 2], ["Table", 2], ["TableGroup", 2], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 6086, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 291], ["Span", 251], ["Line", 41], ["Table", 2], ["Caption", 2], ["TableGroup", 2], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 5935, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 325], ["Span", 255], ["Line", 42], ["Caption", 2], ["Table", 2], ["TableGroup", 2], ["<PERSON>Footer", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 9086, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 216], ["Span", 167], ["Line", 31], ["Table", 3], ["Reference", 2], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 3, "llm_error_count": 1, "llm_tokens_used": 9953, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["TableCell", 241], ["Span", 193], ["Line", 43], ["Table", 2], ["Reference", 2], ["Caption", 1], ["Text", 1], ["<PERSON>Footer", 1], ["TableGroup", 1]], "block_metadata": {"llm_request_count": 2, "llm_error_count": 0, "llm_tokens_used": 4809, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Dataset_Distillation_with_Infinitely_Wide_Convolutional_Networks"}