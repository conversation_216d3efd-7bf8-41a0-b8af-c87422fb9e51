{"table_of_contents": [{"title": "Dataset Distillation for Offline Reinforcement Learning", "heading_level": null, "page_id": 0, "polygon": [[108.7734375, 101.5679931640625], [500.8820495605469, 101.5679931640625], [500.8820495605469, 115.91424560546875], [108.7734375, 115.91424560546875]]}, {"title": "<PERSON>", "heading_level": null, "page_id": 0, "polygon": [[89.2001953125, 133.599853515625], [177.63311767578125, 133.599853515625], [177.63311767578125, 146.2733154296875], [89.2001953125, 146.2733154296875]]}, {"title": "<PERSON><PERSON><PERSON>", "heading_level": null, "page_id": 0, "polygon": [[89.52301025390625, 187.179931640625], [162.70913696289062, 187.179931640625], [162.70913696289062, 199.8543701171875], [89.52301025390625, 199.8543701171875]]}, {"title": "", "heading_level": null, "page_id": 0, "polygon": [[89.947265625, 244.40625], [139.1044921875, 244.40625], [139.1044921875, 255.234375], [89.947265625, 255.234375]]}, {"title": "Editor:", "heading_level": null, "page_id": 0, "polygon": [[89.6484375, 347.7550354003906], [125.128173828125, 347.7550354003906], [125.128173828125, 357.908203125], [89.6484375, 357.908203125]]}, {"title": "Abstract", "heading_level": null, "page_id": 0, "polygon": [[279.10546875, 381.3477478027344], [331.6575927734375, 381.3477478027344], [331.6575927734375, 393.3029479980469], [279.10546875, 393.3029479980469]]}, {"title": "1 Introduction", "heading_level": null, "page_id": 0, "polygon": [[90.0, 513.0086669921875], [176.607421875, 513.0086669921875], [176.607421875, 524.9638671875], [90.0, 524.9638671875]]}, {"title": "2 Methodology", "heading_level": null, "page_id": 1, "polygon": [[89.349609375, 538.9526214599609], [179.6759490966797, 538.9526214599609], [179.6759490966797, 551.07421875], [89.349609375, 551.07421875]]}, {"title": "2.1 Offline reinforcement learning problem setting", "heading_level": null, "page_id": 1, "polygon": [[89.12548828125, 605.5261840820312], [367.857421875, 605.5261840820312], [367.857421875, 616.81640625], [89.12548828125, 616.81640625]]}, {"title": "2.2 Behavioral cloning", "heading_level": null, "page_id": 2, "polygon": [[89.6484375, 456.939208984375], [214.1103515625, 456.939208984375], [214.1103515625, 467.9296875], [89.6484375, 467.9296875]]}, {"title": "2.3 Synthetic dataset", "heading_level": null, "page_id": 3, "polygon": [[89.05078125, 93.29522705078125], [207.8349609375, 93.29522705078125], [207.8349609375, 104.204345703125], [89.05078125, 104.204345703125]]}, {"title": "3 Experimental Setup", "heading_level": null, "page_id": 3, "polygon": [[89.2001953125, 642.1816253662109], [220.9833984375, 642.1816253662109], [220.9833984375, 654.1368255615234], [89.2001953125, 654.1368255615234]]}, {"title": "3.1 Environment", "heading_level": null, "page_id": 4, "polygon": [[89.349609375, 93.29522705078125], [183.31646728515625, 93.29522705078125], [183.31646728515625, 104.204345703125], [89.349609375, 104.204345703125]]}, {"title": "3.2 Model architectures and training", "heading_level": null, "page_id": 4, "polygon": [[89.27490234375, 557.6371917724609], [294.345703125, 557.6371917724609], [294.345703125, 568.5462951660156], [89.27490234375, 568.5462951660156]]}, {"title": "3.2.1 Model training", "heading_level": null, "page_id": 4, "polygon": [[89.12548828125, 575.6418914794922], [208.02557373046875, 575.6418914794922], [208.02557373046875, 588.97265625], [89.12548828125, 588.97265625]]}, {"title": "3.2.2 Data construction", "heading_level": null, "page_id": 5, "polygon": [[88.82666015625, 591.1139068603516], [226.58189392089844, 591.1139068603516], [226.58189392089844, 604.44140625], [88.82666015625, 604.44140625]]}, {"title": "4 Results", "heading_level": null, "page_id": 6, "polygon": [[89.42431640625, 92.4776611328125], [145.73516845703125, 92.4776611328125], [145.73516845703125, 104.432861328125], [89.42431640625, 104.432861328125]]}, {"title": "5 Related work", "heading_level": null, "page_id": 6, "polygon": [[89.2001953125, 625.2936248779297], [180.90733337402344, 625.2936248779297], [180.90733337402344, 637.2488250732422], [89.2001953125, 637.2488250732422]]}, {"title": "5.1 Deep Reinforcement Learning", "heading_level": null, "page_id": 6, "polygon": [[89.05078125, 647.3941955566406], [277.91015625, 647.3941955566406], [277.91015625, 658.58203125], [89.05078125, 658.58203125]]}, {"title": "Table 4: Dataset size used on various data collection methods with respect to\ndifferent environments.", "heading_level": null, "page_id": 7, "polygon": [[89.2001953125, 322.4237060546875], [521.7637329101562, 322.4237060546875], [521.7637329101562, 347.05230712890625], [89.2001953125, 347.05230712890625]]}, {"title": "5.2 Knowledge Distillation", "heading_level": null, "page_id": 7, "polygon": [[89.349609375, 462.515625], [237.8671875, 462.515625], [237.8671875, 474.1171875], [89.349609375, 474.1171875]]}, {"title": "5.3 Policy distillation", "heading_level": null, "page_id": 8, "polygon": [[89.4990234375, 295.7142028808594], [208.7314453125, 295.7142028808594], [208.7314453125, 306.861328125], [89.4990234375, 306.861328125]]}, {"title": "5.4 Dataset Distillation", "heading_level": null, "page_id": 8, "polygon": [[89.349609375, 527.6541748046875], [219.774658203125, 527.6541748046875], [219.774658203125, 538.5632781982422], [89.349609375, 538.5632781982422]]}, {"title": "5.5 Task Generalization", "heading_level": null, "page_id": 9, "polygon": [[89.4990234375, 93.29522705078125], [221.61830139160156, 93.29522705078125], [221.61830139160156, 104.3173828125], [89.4990234375, 104.3173828125]]}, {"title": "5.6 Other Works", "heading_level": null, "page_id": 9, "polygon": [[89.4990234375, 274.2861328125], [183.9287109375, 274.2861328125], [183.9287109375, 285.19525146484375], [89.4990234375, 285.19525146484375]]}, {"title": "6 Conclusion and limitations", "heading_level": null, "page_id": 9, "polygon": [[89.349609375, 457.7396240234375], [259.98046875, 457.7396240234375], [259.98046875, 469.69482421875], [89.349609375, 469.69482421875]]}, {"title": "References", "heading_level": null, "page_id": 10, "polygon": [[89.27490234375, 92.4776611328125], [152.89633178710938, 92.4776611328125], [152.89633178710938, 104.607421875], [89.27490234375, 104.607421875]]}], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 101], ["Line", 42], ["Text", 10], ["SectionHeader", 7], ["<PERSON><PERSON><PERSON><PERSON>", 2], ["Footnote", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 196], ["Line", 45], ["Text", 5], ["ListItem", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1], ["ListGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 603], ["Line", 135], ["Text", 4], ["TextInlineMath", 4], ["Equation", 4], ["Reference", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 160], ["Line", 26], ["SectionHeader", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["Equation", 1], ["Figure", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["FigureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 124], ["Line", 33], ["Text", 4], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Picture", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["PictureGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 147], ["Line", 44], ["TableCell", 24], ["Text", 4], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["SectionHeader", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 250], ["Line", 42], ["TableCell", 29], ["Text", 5], ["SectionHeader", 3], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["Table", 1], ["Caption", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["Reference", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 1, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 194], ["TableCell", 54], ["Line", 32], ["Caption", 3], ["Text", 3], ["Reference", 3], ["Table", 2], ["SectionHeader", 2], ["Figure", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["TableGroup", 1], ["FigureGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 4, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 151], ["Line", 40], ["Text", 3], ["SectionHeader", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["TextInlineMath", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 102], ["Line", 41], ["SectionHeader", 3], ["TextInlineMath", 2], ["Text", 2], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 102], ["Line", 39], ["Reference", 12], ["ListItem", 11], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["SectionHeader", 1], ["Text", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 109], ["Line", 39], ["ListItem", 13], ["Reference", 13], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 70], ["Line", 26], ["ListItem", 9], ["Reference", 9], ["<PERSON><PERSON><PERSON><PERSON>", 1], ["<PERSON>Footer", 1], ["ListGroup", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0, "previous_text": "", "previous_type": "", "previous_order": 0}}], "debug_data_path": "debug_data/Dataset_Distillation_for_Offline_Reinforcement_Learning"}